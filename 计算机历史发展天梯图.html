<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<title>计算机历史发展天梯图 - 三阶段九宫格演进</title>
<style>
    body{font-family:'Microsoft YaHei',<PERSON><PERSON>,sans-serif;margin:0;background:linear-gradient(135deg,#0f0f23 0%,#1a1a2e 100%);color:#eee;min-height:100vh;}
    .container{max-width:1600px;margin:0 auto;padding:20px;}
    h1{text-align:center;font-size:2.8em;margin:30px 0;background:linear-gradient(45deg,#4facfe,#00f2fe);-webkit-background-clip:text;-webkit-text-fill-color:transparent;text-shadow:0 0 30px rgba(79,172,254,0.3);}
    .subtitle{text-align:center;font-size:1.3em;color:#aaa;margin-bottom:40px;}
    .description{max-width:1200px;margin:0 auto 40px;padding:20px;background:rgba(26,26,46,0.8);border-radius:10px;border:1px solid #333;}
    .description h2{color:#4facfe;margin-bottom:15px;}
    .description p{line-height:1.6;color:#ccc;}
    
    .matrix-grid{display:grid;grid-template-columns:180px 1fr 1fr 1fr;grid-template-rows:auto auto auto auto;gap:3px;background:#333;border-radius:12px;overflow:hidden;box-shadow:0 25px 50px rgba(0,0,0,0.4);}
    
    .header-empty{background:#444;}
    .era-header{background:linear-gradient(135deg,#667eea 0%,#764ba2 100%);padding:25px 15px;text-align:center;font-weight:bold;color:#fff;}
    .era-title{font-size:1.2em;margin-bottom:8px;}
    .era-period{font-size:0.9em;opacity:0.9;margin-bottom:5px;}
    .era-core{font-size:0.8em;font-style:italic;opacity:0.8;}
    
    .layer-header{background:linear-gradient(135deg,#4facfe 0%,#00f2fe 100%);padding:20px;display:flex;flex-direction:column;justify-content:center;font-weight:bold;color:#fff;}
    .layer-title{font-size:1.1em;margin-bottom:8px;}
    .layer-desc{font-size:0.8em;opacity:0.9;font-weight:normal;}
    
    .content-cell{background:#1a1a2e;padding:20px;min-height:280px;display:flex;flex-direction:column;transition:all 0.3s ease;border:1px solid transparent;position:relative;}
    .content-cell:hover{background:#252545;border-color:#4facfe;transform:translateY(-3px);box-shadow:0 15px 30px rgba(79,172,254,0.2);}
    
    .cell-title{font-size:1.1em;font-weight:bold;color:#4facfe;margin-bottom:12px;border-bottom:2px solid #4facfe;padding-bottom:8px;}
    .cell-description{font-size:0.85em;color:#bbb;line-height:1.5;margin-bottom:15px;font-style:italic;}
    
    .hardware-section{margin-bottom:12px;}
    .section-title{font-size:0.9em;color:#f5576c;font-weight:bold;margin-bottom:6px;}
    .hardware-items{font-size:0.8em;color:#ccc;line-height:1.5;margin-left:10px;}
    
    .characteristics{margin-top:auto;padding-top:15px;border-top:1px solid #333;}
    .char-title{font-size:0.8em;color:#00f2fe;font-weight:bold;margin-bottom:5px;}
    .char-content{font-size:0.8em;color:#aaa;line-height:1.4;}
    
    .era-indicator{position:absolute;top:10px;right:10px;width:12px;height:12px;border-radius:50%;}
    .era1{background:#FF6B6B;}
    .era2{background:#4ECDC4;}
    .era3{background:#45B7D1;}
    
    .summary{margin-top:50px;background:rgba(26,26,46,0.8);border-radius:12px;padding:30px;border:1px solid #333;}
    .summary h2{color:#4facfe;text-align:center;margin-bottom:30px;font-size:1.8em;}
    .summary-grid{display:grid;grid-template-columns:1fr 1fr 1fr;gap:25px;}
    .summary-item h3{color:#f5576c;margin-bottom:10px;}
    .summary-item p{color:#ccc;line-height:1.6;font-size:0.9em;}
    
    @media (max-width:1400px){
        .matrix-grid{grid-template-columns:150px 1fr 1fr 1fr;font-size:0.9em;}
        .content-cell{min-height:250px;padding:15px;}
        .summary-grid{grid-template-columns:1fr;gap:20px;}
    }
</style>
</head>
<body>
<div class="container">
    <h1>计算机历史发展天梯图</h1>
    <div class="subtitle">三阶段·三层次·硬件演进九宫格</div>
    
    <div class="description">
        <h2>核心框架：硬件发展的九宫格矩阵</h2>
        <p>这个矩阵将三个时代作为列，三个功能层次作为行，清晰地展示了过去几十年来硬件发展的核心脉络。每个节点都可以进行横向（功能层）和纵向（时代）的对比分析。</p>
    </div>
    
    <div class="matrix-grid">
        <!-- 表头行 -->
        <div class="header-empty"></div>
        <div class="era-header">
            <div class="era-title">第一阶段：计算机时代</div>
            <div class="era-period">(约1970s - 1995)</div>
            <div class="era-core">核心：计算的诞生与普及</div>
        </div>
        <div class="era-header">
            <div class="era-title">第二阶段：互联网时代</div>
            <div class="era-period">(约1995 - 2010)</div>
            <div class="era-core">核心：连接的价值</div>
        </div>
        <div class="era-header">
            <div class="era-title">第三阶段：AI与云时代</div>
            <div class="era-period">(约2010 - 至今)</div>
            <div class="era-core">核心：数据的智能</div>
        </div>
        
        <!-- 终端层 -->
        <div class="layer-header">
            <div class="layer-title">① 终端 (Terminal)</div>
            <div class="layer-desc">人机交互的入口</div>
        </div>
        <div class="content-cell">
            <div class="era-indicator era1"></div>
            <div class="cell-title">个人电脑 (PC)</div>
            <div class="cell-description">一体化的计算与交互中心</div>
            
            <div class="hardware-section">
                <div class="section-title">核心硬件：</div>
                <div class="hardware-items">CPU (Intel 8086, 486, Pentium)、内存(RAM)、硬盘(HDD)、主板</div>
            </div>
            
            <div class="hardware-section">
                <div class="section-title">交互硬件：</div>
                <div class="hardware-items">CRT显示器、键盘、鼠标</div>
            </div>
            
            <div class="characteristics">
                <div class="char-title">特征：</div>
                <div class="char-content">终端与计算服务高度统一。PC既是操作的界面，也是所有计算和数据处理发生的地方。它是一个信息孤岛。</div>
            </div>
        </div>
        <div class="content-cell">
            <div class="era-indicator era2"></div>
            <div class="cell-title">多样化接入设备</div>
            <div class="cell-description">PC、笔记本、功能手机，成为信息的窗口</div>
            
            <div class="hardware-section">
                <div class="section-title">核心硬件：</div>
                <div class="hardware-items">PC性能持续增强，显卡(GPU)开始用于图形加速，笔记本电脑普及</div>
            </div>
            
            <div class="hardware-section">
                <div class="section-title">新兴硬件：</div>
                <div class="hardware-items">功能手机(Nokia)、个人数字助理(PDA)、早期智能手机(BlackBerry)</div>
            </div>
            
            <div class="characteristics">
                <div class="char-title">特征：</div>
                <div class="char-content">终端的主要角色从"计算器"转变为"浏览器"或"信息窗口"。硬件设计开始更关注网络连接能力和便携性。</div>
            </div>
        </div>
        <div class="content-cell">
            <div class="era-indicator era3"></div>
            <div class="cell-title">泛在智能终端</div>
            <div class="cell-description">智能手机、IoT设备、可穿戴设备、智能汽车，成为数据的采集器和智能的延伸</div>
            
            <div class="hardware-section">
                <div class="section-title">核心硬件：</div>
                <div class="hardware-items">智能手机的高度集成SoC芯片（集成CPU, GPU, AI处理单元NPU, ISP等）</div>
            </div>
            
            <div class="hardware-section">
                <div class="section-title">新兴硬件：</div>
                <div class="hardware-items">可穿戴设备(智能手表/手环)、IoT设备(智能音箱、摄像头、传感器)、智能汽车、AR/VR头显</div>
            </div>
            
            <div class="characteristics">
                <div class="char-title">特征：</div>
                <div class="char-content">终端从"信息窗口"转变为"数据采集器"和"智能交互入口"。硬件设计极其多样化，内置AI能力（端侧AI），强调低功耗、实时响应和多模态交互。</div>
            </div>
        </div>
        
        <!-- 传输层 -->
        <div class="layer-header">
            <div class="layer-title">② 传输 (Transmission)</div>
            <div class="layer-desc">数据流动的管道</div>
        </div>
        <div class="content-cell">
            <div class="era-indicator era1"></div>
            <div class="cell-title">本地化连接</div>
            <div class="cell-description">主板总线、串口/并口、局域网(LAN)</div>
            
            <div class="hardware-section">
                <div class="section-title">核心硬件：</div>
                <div class="hardware-items">主板上的总线(Bus)是最重要的数据通道。外部通过串口(RS-232)、并口连接打印机等外设</div>
            </div>
            
            <div class="hardware-section">
                <div class="section-title">网络硬件：</div>
                <div class="hardware-items">早期的以太网卡(Ethernet Card)和集线器(Hub)，用于构建局域网(LAN)</div>
            </div>
            
            <div class="characteristics">
                <div class="char-title">特征：</div>
                <div class="char-content">传输距离短、速率慢、协议不统一。主要为了共享昂贵的外设（如打印机）或进行小范围文件交换。</div>
            </div>
        </div>
        <div class="content-cell">
            <div class="era-indicator era2"></div>
            <div class="cell-title">全球互联</div>
            <div class="cell-description">调制解调器(Modem)、DSL、光纤、路由器/交换机、Wi-Fi、2G/3G网络</div>
            
            <div class="hardware-section">
                <div class="section-title">接入硬件：</div>
                <div class="hardware-items">调制解调器(Modem)是家庭上网的标志，从56K拨号到ADSL/Cable Modem（宽带）</div>
            </div>
            
            <div class="hardware-section">
                <div class="section-title">网络骨干：</div>
                <div class="hardware-items">路由器(Router)和交换机(Switch)、全球海底光缆、Wi-Fi (802.11b/g)、2G/3G移动通信</div>
            </div>
            
            <div class="characteristics">
                <div class="char-title">特征：</div>
                <div class="char-content">带宽、延迟和覆盖范围成为关键指标。连接本身成为一种核心能力。</div>
            </div>
        </div>
        <div class="content-cell">
            <div class="era-indicator era3"></div>
            <div class="cell-title">高速泛在网络</div>
            <div class="cell-description">5G/6G、千兆光纤、Wi-Fi 6、卫星互联网、数据中心内部高速互联(InfiniBand)</div>
            
            <div class="hardware-section">
                <div class="section-title">核心硬件：</div>
                <div class="hardware-items">5G基站和终端芯片，Wi-Fi 6/6E提升室内无线体验，千兆光纤入户成为标配</div>
            </div>
            
            <div class="hardware-section">
                <div class="section-title">数据中心内部：</div>
                <div class="hardware-items">为支撑AI集群，出现了InfiniBand、NVLink等超高带宽、超低延迟的专用互联技术</div>
            </div>
            
            <div class="characteristics">
                <div class="char-title">特征：</div>
                <div class="char-content">网络的目标不再仅仅是"连接"，而是要保证海量数据（特别是视频和传感器数据）的实时、可靠传输。</div>
            </div>
        </div>
        
        <!-- 服务层 -->
        <div class="layer-header">
            <div class="layer-title">③ 服务 (Service)</div>
            <div class="layer-desc">计算与存储的核心</div>
        </div>
        <div class="content-cell">
            <div class="era-indicator era1"></div>
            <div class="cell-title">大型机/小型机</div>
            <div class="cell-description">集中的、昂贵的计算资源</div>
            
            <div class="hardware-section">
                <div class="section-title">核心硬件：</div>
                <div class="hardware-items">专有的、高性能的中央处理器和海量存储</div>
            </div>
            
            <div class="hardware-section">
                <div class="section-title">形态：</div>
                <div class="hardware-items">巨大的机柜，需要专门的机房和运维人员</div>
            </div>
            
            <div class="characteristics">
                <div class="char-title">特征：</div>
                <div class="char-content">在PC普及前，计算服务由这些"巨无霸"提供，用户通过哑终端(Dumb Terminal)连接。PC的出现，实际上是将这种集中式服务"打碎"并分布到每个桌面。</div>
            </div>
        </div>
        <div class="content-cell">
            <div class="era-indicator era2"></div>
            <div class="cell-title">分布式服务器/数据中心</div>
            <div class="cell-description">网站和应用的后台载体</div>
            
            <div class="hardware-section">
                <div class="section-title">核心硬件：</div>
                <div class="hardware-items">标准化的机架式服务器(Rack Server)和刀片服务器(Blade Server)取代了昂贵的小型机</div>
            </div>
            
            <div class="hardware-section">
                <div class="section-title">存储硬件：</div>
                <div class="hardware-items">网络附加存储(NAS)和存储区域网络(SAN)出现，实现数据集中存储和管理</div>
            </div>
            
            <div class="characteristics">
                <div class="char-title">特征：</div>
                <div class="char-content">客户端-服务器(Client-Server)架构成为主流。服务从单一机器扩展为由大量服务器组成的集群，追求的是高可用性和可扩展性。</div>
            </div>
        </div>
        <div class="content-cell">
            <div class="era-indicator era3"></div>
            <div class="cell-title">超大规模云数据中心/边缘计算</div>
            <div class="cell-description">全球化的、按需分配的智能与算力</div>
            
            <div class="hardware-section">
                <div class="section-title">AI加速器：</div>
                <div class="hardware-items">GPU(NVIDIA)、ASIC(Google TPU)、FPGA提供灵活的硬件加速方案</div>
            </div>
            
            <div class="hardware-section">
                <div class="section-title">形态：</div>
                <div class="hardware-items">超大规模(Hyperscale)数据中心(AWS、Azure、Google Cloud)，边缘计算(Edge Computing)节点</div>
            </div>
            
            <div class="characteristics">
                <div class="char-title">特征：</div>
                <div class="char-content">服务层从提供"资源"升级为提供"能力(Capability)"，特别是AI训练和推理的能力。硬件设计走向异构计算和领域专用架构(DSA)。</div>
            </div>
        </div>
    </div>
    
    <div class="summary">
        <h2>硬件发展脉络总结</h2>
        <div class="summary-grid">
            <div class="summary-item">
                <h3>驱动力变迁</h3>
                <p>从计算驱动到连接驱动，再到数据/智能驱动。每个时代都有其核心的技术推动力和商业模式。</p>
            </div>
            <div class="summary-item">
                <h3>终端演进</h3>
                <p>从"胖"到"瘦"，再到"智能"。PC时代终端是全能的（胖）；互联网时代终端是浏览器（瘦）；AI时代终端是传感器和AI助理（智能）。</p>
            </div>
            <div class="summary-item">
                <h3>服务架构</h3>
                <p>从"集中"到"分散"，再到"云合/边散"的新范式。大型机是集中；PC是分散；云计算是新的超级集中，而边缘计算又是一种新的分散。</p>
            </div>
        </div>
    </div>

    <div class="timeline-section">
        <h2>关键时间节点</h2>
        <div class="timeline-container">
            <div class="timeline-item era1">
                <div class="timeline-year">1970s-1995</div>
                <div class="timeline-content">
                    <h4>计算机时代</h4>
                    <p>算力的"民主化"，从大型机走入办公室和家庭</p>
                    <ul>
                        <li>1971: Intel 4004 - 第一款商用微处理器</li>
                        <li>1981: IBM PC - 个人电脑标准确立</li>
                        <li>1984: Apple Macintosh - GUI普及</li>
                        <li>1993: Intel Pentium - PC性能飞跃</li>
                    </ul>
                </div>
            </div>
            <div class="timeline-item era2">
                <div class="timeline-year">1995-2010</div>
                <div class="timeline-content">
                    <h4>互联网时代</h4>
                    <p>连接成为核心价值，硬件为网络服务</p>
                    <ul>
                        <li>1995: Windows 95 + 网景浏览器</li>
                        <li>1999: Wi-Fi标准确立</li>
                        <li>2007: iPhone - 移动互联网革命</li>
                        <li>2008: App Store - 移动生态建立</li>
                    </ul>
                </div>
            </div>
            <div class="timeline-item era3">
                <div class="timeline-year">2010-至今</div>
                <div class="timeline-content">
                    <h4>AI与云时代</h4>
                    <p>数据与智能驱动，硬件为AI算法服务</p>
                    <ul>
                        <li>2012: AlexNet - 深度学习突破</li>
                        <li>2016: AlphaGo - AI里程碑</li>
                        <li>2020: GPT-3 - 大语言模型</li>
                        <li>2023: ChatGPT - AI普及应用</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div class="controls">
        <h3>交互控制</h3>
        <div class="control-buttons">
            <button class="control-btn active" onclick="showAll()">显示全部</button>
            <button class="control-btn" onclick="showEra(1)">计算机时代</button>
            <button class="control-btn" onclick="showEra(2)">互联网时代</button>
            <button class="control-btn" onclick="showEra(3)">AI与云时代</button>
        </div>
        <div class="layer-buttons">
            <button class="layer-btn" onclick="highlightLayer('terminal')">终端层</button>
            <button class="layer-btn" onclick="highlightLayer('transmission')">传输层</button>
            <button class="layer-btn" onclick="highlightLayer('service')">服务层</button>
            <button class="layer-btn" onclick="highlightLayer('none')">清除高亮</button>
        </div>
    </div>
</div>

<script>
function showAll() {
    document.querySelectorAll('.content-cell').forEach(cell => {
        cell.style.opacity = '1';
        cell.style.filter = 'none';
    });
    document.querySelectorAll('.control-btn').forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');
}

function showEra(era) {
    document.querySelectorAll('.content-cell').forEach(cell => {
        const indicator = cell.querySelector('.era-indicator');
        if (indicator && indicator.classList.contains(`era${era}`)) {
            cell.style.opacity = '1';
            cell.style.filter = 'none';
        } else {
            cell.style.opacity = '0.3';
            cell.style.filter = 'grayscale(70%)';
        }
    });
    document.querySelectorAll('.control-btn').forEach(btn => btn.classList.remove('active'));
    event.target.classList.add('active');
}

function highlightLayer(layer) {
    document.querySelectorAll('.content-cell').forEach(cell => {
        cell.classList.remove('highlight-terminal', 'highlight-transmission', 'highlight-service');
    });

    if (layer !== 'none') {
        const layerCells = document.querySelectorAll(`.content-cell:nth-child(${getLayerIndex(layer)}n+${getLayerStart(layer)})`);
        layerCells.forEach(cell => {
            cell.classList.add(`highlight-${layer}`);
        });
    }
}

function getLayerIndex(layer) {
    return 4; // 4列网格
}

function getLayerStart(layer) {
    switch(layer) {
        case 'terminal': return 2; // 第2、3、4列
        case 'transmission': return 6; // 第6、7、8列
        case 'service': return 10; // 第10、11、12列
        default: return 0;
    }
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('计算机历史发展天梯图加载完成');

    // 添加鼠标悬停效果
    document.querySelectorAll('.content-cell').forEach(cell => {
        cell.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px) scale(1.02)';
        });

        cell.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
});
</script>

<style>
.timeline-section{margin-top:50px;background:rgba(26,26,46,0.8);border-radius:12px;padding:30px;border:1px solid #333;}
.timeline-section h2{color:#4facfe;text-align:center;margin-bottom:30px;font-size:1.8em;}
.timeline-container{display:flex;gap:20px;justify-content:space-between;}
.timeline-item{flex:1;background:#252545;border-radius:8px;padding:20px;border-left:4px solid;}
.timeline-item.era1{border-left-color:#FF6B6B;}
.timeline-item.era2{border-left-color:#4ECDC4;}
.timeline-item.era3{border-left-color:#45B7D1;}
.timeline-year{font-size:1.2em;font-weight:bold;color:#4facfe;margin-bottom:10px;}
.timeline-content h4{color:#f5576c;margin-bottom:8px;}
.timeline-content p{color:#ccc;font-size:0.9em;margin-bottom:10px;}
.timeline-content ul{color:#aaa;font-size:0.8em;line-height:1.5;}

.controls{margin-top:40px;text-align:center;background:rgba(26,26,46,0.8);border-radius:12px;padding:25px;border:1px solid #333;}
.controls h3{color:#4facfe;margin-bottom:20px;}
.control-buttons,.layer-buttons{margin-bottom:15px;}
.control-btn,.layer-btn{background:#333;color:#fff;border:1px solid #555;padding:10px 20px;margin:0 5px;border-radius:6px;cursor:pointer;transition:all 0.3s ease;}
.control-btn:hover,.layer-btn:hover{background:#555;border-color:#777;}
.control-btn.active{background:#4facfe;border-color:#4facfe;}

.highlight-terminal{border-color:#FF6B6B !important;box-shadow:0 0 20px rgba(255,107,107,0.3) !important;}
.highlight-transmission{border-color:#4ECDC4 !important;box-shadow:0 0 20px rgba(78,205,196,0.3) !important;}
.highlight-service{border-color:#45B7D1 !important;box-shadow:0 0 20px rgba(69,183,209,0.3) !important;}

@media (max-width:1200px){
    .timeline-container{flex-direction:column;gap:15px;}
    .control-buttons,.layer-buttons{display:flex;flex-wrap:wrap;justify-content:center;}
    .control-btn,.layer-btn{margin:5px;padding:8px 15px;font-size:0.9em;}
}
</style>

</body>
</html>

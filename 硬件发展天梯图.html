<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<title>计算机-互联网-AI 硬件天梯图（1940-2024）</title>
<style>
    body{font-family:Arial,Helvetica,sans-serif;margin:0;background:#111;color:#eee;}
    h1{text-align:center;margin:20px 0;}
    .ladder{display:flex;flex-direction:column-reverse;align-items:center;width:100%;}
    .year-bar{width:90%;max-width:1200px;display:flex;border-top:1px solid #444;margin-bottom:2px;}
    .year-label{width:80px;flex-shrink:0;text-align:center;font-weight:bold;padding:6px 0;}
    .step{display:flex;flex:1;height:auto;min-height:70px;overflow:hidden;}
    .step>div{flex:1;display:flex;flex-direction:column;justify-content:center;
              align-items:center;font-size:12px;padding:8px 4px;border-left:1px solid #333;}
    .step .success{background:#003300;}
    .step .fail{background:#330000;}
    .step .ongoing{background:#004400;font-weight:bold;}
    .legend{margin:20px auto;width:90%;max-width:600px;font-size:13px;
            display:flex;justify-content:space-around;}
    .legend span{padding:4px 8px;border-radius:4px;margin-right:6px;}
    .tabs{text-align:center;margin:10px 0;}
    .tab-button{background:#333;color:#fff;border:1px solid #555;padding:8px 16px;cursor:pointer;margin:0 4px;border-radius:5px;}
    .tab-button.active{background:#555;border-color:#777;}
    .tabs-container.stage-tabs-container { text-align: center; margin-top: 10px; }
    #secondary-tabs, #company-tabs{text-align:center;margin:10px 0;min-height:30px;}
    .secondary-button, .company-button, .stage-button{background:#2a2a2a;color:#ccc;border:1px solid #444;padding:5px 10px;cursor:pointer;margin:0 3px 5px;border-radius:4px;font-size:12px;}
    .stage-button.active{background:#444;border-color:#666;}
    .secondary-button.active, .company-button.active{background:#444;border-color:#666;}
    .step > div { display: none; }
    .step > div.active { display: flex; }
    .description{font-size:11px;color:#bbb;margin-top:4px;font-style:italic;}
    .description-container { display: flex; justify-content: space-around; max-width: 1200px; margin: 20px auto; padding: 10px; background: #1a1a1a; border-radius: 8px; }
    .phase { flex: 1; padding: 0 15px; border-left: 1px solid #444; }
    .phase:first-child { border-left: none; }
    .phase h2 { font-size: 16px; color: #eee; margin-top: 0; }
    .phase p { font-size: 13px; color: #ccc; line-height: 1.6; }
</style>
</head>
<body>
<h1>硬件层次发展图</h1>
<div class="description-container">
    <div class="phase">
        <h2>第一阶段: 计算机时代 (约1970s - 1995)</h2>
        <p><strong>核心:</strong> 计算的诞生与普及</p>
        <p><strong>① 终端 (Terminal):</strong> 个人电脑(PC): 一体化的计算与交互中心。</p>
        <p><strong>② 传输 (Transmission):</strong> 本地化连接: 主板总线、串口/并口、局域网(LAN)。</p>
        <p><strong>③ 服务 (Service):</strong> 大型机/小型机: 集中的、昂贵的计算资源。</p>
    </div>
    <div class="phase">
        <h2>第二阶段: 互联网时代 (约1995 - 2010)</h2>
        <p><strong>核心:</strong> 连接的价值</p>
        <p><strong>① 终端 (Terminal):</strong> 多样化接入设备: PC、笔记本、功能手机, 成为信息的窗口。</p>
        <p><strong>② 传输 (Transmission):</strong> 全球互联: 调制解调器(Modem)、DSL、光纤、路由器/交换机、Wi-Fi、2G/3G网络。</p>
        <p><strong>③ 服务 (Service):</strong> 分布式服务器/数据中心: 网站和应用的后台载体。</p>
    </div>
    <div class="phase">
        <h2>第三阶段: AI与云时代 (约2010 - 至今)</h2>
        <p><strong>核心:</strong> 数据的智能</p>
        <p><strong>① 终端 (Terminal):</strong> 泛在智能终端: 智能手机、IoT设备、可穿戴设备、智能汽车, 成为数据的采集器和智能的延伸。</p>
        <p><strong>② 传输 (Transmission):</strong> 高速泛在网络: 5G/6G、千兆光纤、Wi-Fi 6、卫星互联网、数据中心内部高速互联(InfiniBand)。</p>
        <p><strong>③ 服务 (Service):</strong> 超大规模云数据中心/边缘计算: 全球化的、按需分配的智能与算力。</p>
    </div>
</div>

<div class="tabs">
    <button class="tab-button active" onclick="filterByCategory('终端', this)">① 终端</button>
    <button class="tab-button" onclick="filterByCategory('传输', this)">② 传输</button>
    <button class="tab-button" onclick="filterByCategory('服务', this)">③ 服务</button>
</div>
<div class="tabs-container stage-tabs-container">
    <button class="stage-button" onclick="filterByStage('All', this)">All Stages</button>
    <button class="stage-button" onclick="filterByStage('1', this)">第一阶段</button>
    <button class="stage-button" onclick="filterByStage('2', this)">第二阶段</button>
    <button class="stage-button" onclick="filterByStage('3', this)">第三阶段</button>
</div>
<div id="secondary-tabs" class="secondary-tabs-container"></div>
<div id="company-tabs" class="company-tabs-container"></div>

<div class="legend">
    <span class="success">成功</span>
    <span class="fail">失败 / 退出</span>
    <span class="ongoing">仍在运营</span>
</div>

<div class="ladder">
<!--LADDER_CONTENT_START-->
<!-- New content will be inserted here based on 需求.md -->

<!-- 第三阶段：AI与云时代 (约2010 - 至今) -->

<!-- 2024 -->
<div class="year-bar">
    <div class="year-label">2024</div>
    <div class="step">
        <div class="ongoing active" data-stage="3" data-category="终端" data-industry="半导体/核心硬件" data-company="Apple">Apple M4<br><span class="description">苹果最新的SoC，集成了强大的神经引擎。</span></div>
        <div class="ongoing" data-stage="3" data-category="终端" data-industry="半导体/核心硬件" data-company="Qualcomm">Snapdragon X Elite<br><span class="description">高通专为AI PC设计的ARM处理器，挑战英特尔。</span></div>
        <div class="ongoing" data-stage="3" data-category="传输" data-industry="网络/标准" data-company="IEEE">Wi-Fi 7<br><span class="description">最新的Wi-Fi标准，理论速度高达46 Gbps。</span></div>
        <div class="ongoing" data-stage="3" data-category="服务" data-industry="半导体/核心硬件" data-company="Nvidia">Nvidia H200<br><span class="description">用于训练AI大模型的顶级显卡。</span></div>
    </div>
</div>

<!-- 2023 -->
<div class="year-bar">
    <div class="year-label">2023</div>
    <div class="step">
        <div class="ongoing active" data-stage="3" data-category="终端" data-industry="AR/VR" data-company="Apple">Apple Vision Pro<br><span class="description">苹果首款空间计算设备，开启混合现实新时代。</span></div>
        <div class="ongoing" data-stage="3" data-category="传输" data-industry="网络/标准" data-company="光通信行业">800G 光模块<br><span class="description">满足AI和机器学习集群对极端带宽的需求。</span></div>
        <div class="ongoing" data-stage="3" data-category="服务" data-industry="半导体/核心硬件" data-company="Google">Google TPU v5e<br><span class="description">谷歌为AI推理设计的专用芯片。</span></div>
    </div>
</div>

<!-- 2021 -->
<div class="year-bar">
    <div class="year-label">2021</div>
    <div class="step">
        <div class="ongoing active" data-stage="3" data-category="终端" data-industry="半导体/核心硬件" data-company="Apple">Apple M1 Max/Pro<br><span class="description">苹果自研的高性能SoC，用于MacBook Pro。</span></div>
        <div class="ongoing" data-stage="3" data-category="传输" data-industry="网络/标准" data-company="JEDEC">DDR5 SDRAM<br><span class="description">新一代内存标准，提供了更高的数据传输速率。</span></div>
        <div class="ongoing" data-stage="3" data-category="服务" data-industry="半导体/核心硬件" data-company="Google">Google TPU v4i<br><span class="description">谷歌专为AI推理设计的芯片。</span></div>
    </div>
</div>

<!-- 2019 -->
<div class="year-bar">
    <div class="year-label">2019</div>
    <div class="step">
        <div class="ongoing active" data-stage="3" data-category="终端" data-industry="半导体/核心硬件" data-company="AMD">AMD Ryzen 3000<br><span class="description">采用7nm工艺和Zen 2架构，在PC市场与英特尔展开激烈竞争。</span></div>
        <div class="ongoing" data-stage="3" data-category="传输" data-industry="网络/标准" data-company="IEEE">Wi-Fi 6<br><span class="description">新一代Wi-Fi标准，提升了速度、容量和效率。</span></div>
        <div class="ongoing" data-stage="3" data-category="服务" data-industry="半导体/核心硬件" data-company="Google">Google Edge TPU<br><span class="description">谷歌为在边缘设备上运行AI而设计的芯片。</span></div>
    </div>
</div>

<!-- 2018 -->
<div class="year-bar">
    <div class="year-label">2018</div>
    <div class="step">
        <div class="ongoing active" data-stage="3" data-category="终端" data-industry="半导体/核心硬件" data-company="Apple">iPhone XS<br><span class="description">搭载A12 Bionic芯片，进一步提升了移动AI性能。</span></div>
        <div class="ongoing" data-stage="3" data-category="服务" data-industry="软件/服务/生态" data-company="Amazon">AWS Nitro<br><span class="description">AWS的虚拟化硬件，提升了云服务器的性能和安全性。</span></div>
        <div class="ongoing" data-stage="3" data-category="服务" data-industry="半导体/核心硬件" data-company="Nvidia">Nvidia Turing<br><span class="description">引入实时光线追踪（RTX）和DLSS技术。</span></div>
    </div>
</div>

<!-- 2017 -->
<div class="year-bar">
    <div class="year-label">2017</div>
    <div class="step">
        <div class="ongoing active" data-stage="3" data-category="终端" data-industry="半导体/核心硬件" data-company="Apple">iPhone X<br><span class="description">引入Face ID和神经引擎，推动了移动AI应用。</span></div>
        <div class="ongoing" data-stage="3" data-category="服务" data-industry="半导体/核心硬件" data-company="Google">Google TPU v2<br><span class="description">谷歌的第二代TPU，可用于训练和推理。</span></div>
        <div class="ongoing" data-stage="3" data-category="服务" data-industry="半导体/核心硬件" data-company="Nvidia">Nvidia Tesla V100<br><span class="description">引入Tensor Core，极大地加速了AI训练。</span></div>
    </div>
</div>

<!-- 2016 -->
<div class="year-bar">
    <div class="year-label">2016</div>
    <div class="step">
        <div class="ongoing active" data-stage="3" data-category="终端" data-industry="软件/服务/生态" data-company="DeepMind/Google">AlphaGo<br><span class="description">击败了世界围棋冠军，是AI发展史上的里程碑。</span></div>
        <div class="ongoing" data-stage="3" data-category="传输" data-industry="网络/标准" data-company="光通信行业">光模块 (QSFP28)<br><span class="description">成为100G数据中心网络的主流方案。</span></div>
        <div class="ongoing" data-stage="3" data-category="服务" data-industry="半导体/核心硬件" data-company="Nvidia">Nvidia DGX-1<br><span class="description">第一台专为深度学习设计的“AI超级计算机”。</span></div>
    </div>
</div>

<!-- 第二阶段：互联网时代 (约1995 - 2010) -->

<!-- 2010 -->
<div class="year-bar">
    <div class="year-label">2010</div>
    <div class="step">
        <div class="success active" data-stage="2" data-category="终端" data-industry="半导体/核心硬件" data-company="Apple">iPad<br><span class="description">开创了平板电脑市场，成为PC和手机之间的重要补充。</span></div>
        <div class="success" data-stage="2" data-category="终端" data-industry="半导体/核心硬件" data-company="Samsung">Samsung Galaxy S<br><span class="description">安卓阵营的旗舰手机，开始挑战iPhone。</span></div>
        <div class="success" data-stage="2" data-category="服务" data-industry="软件/服务/生态" data-company="Microsoft">Microsoft Azure<br><span class="description">微软的云计算平台，成为AWS的主要竞争对手。</span></div>
    </div>
</div>

<!-- 2008 -->
<div class="year-bar">
    <div class="year-label">2008</div>
    <div class="step">
        <div class="success active" data-stage="2" data-category="终端" data-industry="软件/服务/生态" data-company="Apple">App Store<br><span class="description">为移动设备创建了一个繁荣的应用生态系统。</span></div>
        <div class="success" data-stage="2" data-category="终端" data-industry="半导体/核心硬件" data-company="HTC">HTC Dream (G1)<br><span class="description">第一款商用安卓手机。</span></div>
        <div class="success" data-stage="2" data-category="服务" data-industry="半导体/核心硬件" data-company="Intel">Intel Atom<br><span class="description">专为上网本和移动设备设计的低功耗处理器。</span></div>
    </div>
</div>

<!-- 2007 -->
<div class="year-bar">
    <div class="year-label">2007</div>
    <div class="step">
        <div class="success active" data-stage="2" data-category="终端" data-industry="半导体/核心硬件" data-company="Apple">iPhone<br><span class="description">重新定义了智能手机，开启了移动互联网时代。</span></div>
        <div class="success" data-stage="2" data-category="终端" data-industry="软件/服务/生态" data-company="Amazon">Kindle<br><span class="description">电子书阅读器，改变了人们阅读和购买书籍的方式。</span></div>
        <div style="flex:1;"></div>
    </div>
</div>

<!-- 2006 -->
<div class="year-bar">
    <div class="year-label">2006</div>
    <div class="step">
        <div class="success" data-stage="2" data-category="传输" data-industry="网络/标准" data-company="3GPP">3G<br><span class="description">第三代移动通信技术，使得移动设备可以真正上网。</span></div>
        <div class="success" data-stage="2" data-category="服务" data-industry="软件/服务/生态" data-company="Amazon">Amazon Web Services (AWS)<br><span class="description">开创了云计算市场，提供了弹性的计算和存储服务。</span></div>
        <div class="success" data-stage="2" data-category="服务" data-industry="半导体/核心硬件" data-company="Intel">Intel Core 2 Duo<br><span class="description">非常成功的双核处理器，广泛用于PC和笔记本电脑。</span></div>
    </div>
</div>

<!-- 2001 -->
<div class="year-bar">
    <div class="year-label">2001</div>
    <div class="step">
        <div class="success active" data-stage="2" data-category="终端" data-industry="半导体/核心硬件" data-company="Apple">iPod<br><span class="description">数字音乐播放器，改变了音乐产业。</span></div>
        <div class="fail" data-stage="2" data-category="终端" data-industry="半导体/核心硬件" data-company="Microsoft">Tablet PC<br><span class="description">微软早期的平板电脑尝试，但未获成功。</span></div>
        <div class="success" data-stage="2" data-category="传输" data-industry="网络/标准" data-company="IEEE">Wi-Fi (802.11b)<br><span class="description">Wi-Fi开始普及，让无线局域网成为可能。</span></div>
    </div>
</div>

<!-- 1999 -->
<div class="year-bar">
    <div class="year-label">1999</div>
    <div class="step">
        <div class="success" data-stage="2" data-category="传输" data-industry="网络/标准" data-company="IEEE">802.11a/b<br><span class="description">Wi-Fi标准的早期版本，为无线局域网奠定了基础。</span></div>
        <div class="success" data-stage="2" data-category="服务" data-industry="半导体/核心硬件" data-company="Nvidia">GeForce 256<br><span class="description">第一款GPU，将图形处理从CPU中分离出来。</span></div>
        <div class="fail" data-stage="2" data-category="终端" data-industry="半导体/核心硬件" data-company="RIM">BlackBerry 850<br><span class="description">早期的智能手机，以其邮件推送功能而闻名。</span></div>
    </div>
</div>

<!-- 第一阶段：计算机时代 (约1970s - 1995) -->

<!-- 1993 -->
<div class="year-bar">
    <div class="year-label">1993</div>
    <div class="step">
        <div class="success" data-stage="1" data-category="终端" data-industry="半导体/核心硬件" data-company="Intel">Pentium<br><span class="description">英特尔的第五代微处理器，极大地提升了PC性能。</span></div>
        <div style="flex:2;"></div>
    </div>
</div>

<!-- 1989 -->
<div class="year-bar">
    <div class="year-label">1989</div>
    <div class="step">
        <div class="success" data-stage="1" data-category="终端" data-industry="半导体/核心硬件" data-company="Intel">Intel 486<br><span class="description">第一款内置数学协处理器的x86处理器。</span></div>
        <div style="flex:2;"></div>
    </div>
</div>

<!-- 1985 -->
<div class="year-bar">
    <div class="year-label">1985</div>
    <div class="step">
        <div class="success" data-stage="1" data-category="终端" data-industry="半导体/核心硬件" data-company="Intel">Intel 386<br><span class="description">第一款32位x86处理器，是PC架构的一个重要里程碑。</span></div>
        <div style="flex:2;"></div>
    </div>
</div>

<!-- 1984 -->
<div class="year-bar">
    <div class="year-label">1984</div>
    <div class="step">
        <div class="success active" data-stage="1" data-category="终端" data-industry="半导体/核心硬件" data-company="Apple">Macintosh<br><span class="description">第一款商业上成功的带有图形用户界面的个人电脑。</span></div>
        <div style="flex:2;"></div>
    </div>
</div>

<!-- 1981 -->
<div class="year-bar">
    <div class="year-label">1981</div>
    <div class="step">
        <div class="success active" data-stage="1" data-category="终端" data-industry="半导体/核心硬件" data-company="IBM">IBM PC<br><span class="description">开创了个人电脑市场，并建立了开放标准。</span></div>
        <div class="success" data-stage="1" data-category="终端" data-industry="半导体/核心硬件" data-company="Microsoft">MS-DOS<br><span class="description">IBM PC的操作系统，成为PC软件的基础。</span></div>
        <div style="flex:1;"></div>
    </div>
</div>

<!-- 1978 -->
<div class="year-bar">
    <div class="year-label">1978</div>
    <div class="step">
        <div class="success" data-stage="1" data-category="终端" data-industry="半导体/核心硬件" data-company="Intel">Intel 8086<br><span class="description">16位微处理器，奠定了x86架构的基础。</span></div>
        <div style="flex:2;"></div>
    </div>
</div>

<!-- 1977 -->
<div class="year-bar">
    <div class="year-label">1977</div>
    <div class="step">
        <div class="success active" data-stage="1" data-category="终端" data-industry="半导体/核心硬件" data-company="Apple">Apple II<br><span class="description">非常成功的个人电脑，推动了PC革命。</span></div>
        <div style="flex:2;"></div>
    </div>
</div>

<!-- 1976 -->
<div class="year-bar">
    <div class="year-label">1976</div>
    <div class="step">
        <div class="success" data-stage="1" data-category="服务" data-industry="半导体/核心硬件" data-company="Cray">Cray-1<br><span class="description">第一台商业上成功的超级计算机。</span></div>
        <div style="flex:2;"></div>
    </div>
</div>

<!-- 1973 -->
<div class="year-bar">
    <div class="year-label">1973</div>
    <div class="step">
        <div class="success" data-stage="1" data-category="传输" data-industry="科研/教育" data-company="Xerox PARC">以太网<br><span class="description">定义了有线局域网的标准，至今仍在使用。</span></div>
        <div style="flex:2;"></div>
    </div>
</div>

<!-- 1971 -->
<div class="year-bar">
    <div class="year-label">1971</div>
    <div class="step">
        <div class="success" data-stage="1" data-category="服务" data-industry="半导体/核心硬件" data-company="Intel">Intel 4004<br><span class="description">第一款商用微处理器，将整个CPU集成到单颗芯片上。</span></div>
        <div class="success active" data-stage="1" data-category="终端" data-industry="半导体/核心硬件" data-company="IBM">8"软盘<br><span class="description">第一个便携式、可重复写入的存储介质。</span></div>
        <div class="fail" data-stage="1" data-category="服务" data-industry="半导体/核心硬件" data-company="DEC">DEC PDP-11<br><span class="description">非常成功的小型机，广泛用于各种应用。</span></div>
    </div>
</div>

<!-- 1969 -->
<div class="year-bar">
    <div class="year-label">1969</div>
    <div class="step">
        <div class="fail" data-stage="1" data-category="传输" data-industry="网络/标准" data-company="BBN">ARPANET IMP<br><span class="description">互联网的前身，用于连接不同研究机构的计算机。</span></div>
        <div class="success" data-stage="1" data-category="服务" data-industry="软件/服务/生态" data-company="AT&T/Bell">Unix<br><span class="description">一个强大且可移植的操作系统，影响了后来的许多OS。</span></div>
        <div class="success" data-stage="1" data-category="服务" data-industry="半导体/核心硬件" data-company="Honeywell">Honeywell 316<br><span class="description">作为IMP的一部分，是早期互联网的关键硬件。</span></div>
    </div>
</div>

<!-- 1964 -->
<div class="year-bar">
    <div class="year-label">1964</div>
    <div class="step">
        <div class="success" data-stage="1" data-category="服务" data-industry="半导体/核心硬件" data-company="IBM">IBM System/360<br><span class="description">首个实现了指令集架构兼容的计算机系列。</span></div>
        <div class="success" data-stage="1" data-category="传输" data-industry="半导体/核心硬件" data-company="IBM">9-track磁带<br><span class="description">成为行业标准的数据存储和交换格式。</span></div>
        <div class="success active" data-stage="1" data-category="终端" data-industry="科研/教育" data-company="Dartmouth">BASIC语言<br><span class="description">一种易于学习的编程语言，推动了个人计算的普及。</span></div>
    </div>
</div>

<!-- 1995 -->
<div class="year-bar">
    <div class="year-label">1995</div>
    <div class="step">
        <div class="success" data-stage="2" data-category="传输" data-industry="网络/标准" data-company="IETF">HTTP/1.0<br><span class="description">定义了Web浏览器和服务器之间的通信协议。</span></div>
        <div class="success" data-stage="2" data-category="传输" data-industry="网络/标准" data-company="USB-IF">USB 1.0<br><span class="description">一种通用的串行总线标准，简化了外设的连接。</span></div>
        <div class="success" data-stage="2" data-category="服务" data-industry="软件/服务/生态" data-company="Netscape">Netscape Navigator<br><span class="description">第一款商业上成功的Web浏览器，推动了互联网的普及。</span></div>
    </div>
</div>

<!-- 2015 -->
<div class="year-bar">
    <div class="year-label">2015</div>
    <div class="step">
        <div class="ongoing active" data-stage="3" data-category="终端" data-industry="半导体/核心硬件" data-company="Apple">Apple Watch<br><span class="description">苹果进入可穿戴设备市场，定义了现代智能手表。</span></div>
        <div class="ongoing" data-stage="3" data-category="终端" data-industry="软件/服务/生态" data-company="Amazon">Amazon Echo<br><span class="description">内置Alexa语音助手，开创了智能音箱市场。</span></div>
        <div class="ongoing" data-stage="3" data-category="服务" data-industry="半导体/核心硬件" data-company="Google">Google TPU v1<br><span class="description">谷歌为加速AI计算而设计的专用芯片，首次用于AlphaGo。</span></div>
    </div>
</div>

<!-- 2020 -->
<div class="year-bar">
    <div class="year-label">2020</div>
    <div class="step">
        <div class="ongoing active" data-stage="3" data-category="终端" data-industry="软件/服务/生态" data-company="OpenAI">GPT-3<br><span class="description">拥有1750亿参数的语言模型，展示了AI生成内容的巨大潜力。</span></div>
        <div class="ongoing" data-stage="3" data-category="传输" data-industry="网络/标准" data-company="3GPP">5G<br><span class="description">第五代移动通信技术，提供更高的带宽和更低的时延。</span></div>
        <div class="ongoing" data-stage="3" data-category="服务" data-industry="半导体/核心硬件" data-company="Nvidia">Nvidia A100<br><span class="description">专为AI、数据分析和高性能计算设计的GPU。</span></div>
    </div>
</div>

<!-- 2022 -->
<div class="year-bar">
    <div class="year-label">2022</div>
    <div class="step">
        <div class="ongoing active" data-stage="3" data-category="终端" data-industry="半导体/核心硬件" data-company="Apple">Apple M2<br><span class="description">苹果的第二代自研SoC。</span></div>
        <div class="ongoing" data-stage="3" data-category="传输" data-industry="网络/标准" data-company="CXL Consortium">CXL<br><span class="description">一种开放的互连协议，用于连接CPU、内存和加速器。</span></div>
        <div class="ongoing" data-stage="3" data-category="服务" data-industry="半导体/核心硬件" data-company="Nvidia">Nvidia H100<br><span class=

<!-- 2024 -->
    <div class="year-bar">
        <div class="year-label">2024</div>
        <div class="step">
            <div class="ongoing active" data-stage="3" data-category="终端" data-industry="半导体/核心硬件" data-company="Apple">Apple M4<br><span class="description">苹果最新的SoC，集成了强大的神经引擎。</span></div>
            <div class="ongoing" data-stage="3" data-category="终端" data-industry="半导体/核心硬件" data-company="Qualcomm">Snapdragon X Elite<br><span class="description">高通专为AI PC设计的ARM处理器，挑战英特尔。</span></div>
            <div class="ongoing" data-stage="3" data-category="传输" data-industry="网络/标准" data-company="IEEE">Wi-Fi 7<br><span class="description">最新的Wi-Fi标准，理论速度高达46 Gbps。</span></div>
            <div class="ongoing" data-stage="3" data-category="传输" data-industry="网络/标准" data-company="USB-IF">USB4 v2.0<br><span class="description">80Gbps的USB标准，支持更高的数据传输速率。</span></div>
            <div class="ongoing" data-stage="3" data-category="服务" data-industry="半导体/核心硬件" data-company="Nvidia">Nvidia H200<br><span class="description">用于训练AI大模型的顶级显卡，配备141GB HBM3e内存。</span></div>
            <div class="ongoing" data-stage="3" data-category="服务" data-industry="半导体/核心硬件" data-company="AMD">AMD Instinct MI300X<br><span class="description">AMD的AI加速器，拥有192GB HBM3内存。</span></div>
            <div class="ongoing" data-stage="3" data-category="服务" data-industry="量子计算" data-company="IBM">IBM Quantum Condor<br><span class="description">1000+量子比特的量子处理器，量子优势的重要里程碑。</span></div>
        </div>
    </div>

<!-- 2023 -->
    <div class="year-bar">
        <div class="year-label">2023</div>
        <div class="step">
            <div class="ongoing active" data-stage="3" data-category="终端" data-industry="软件/服务/生态" data-company="OpenAI">ChatGPT App<br><span class="description">将强大的AI聊天机器人带到移动端。</span></div>
            <div class="ongoing" data-stage="3" data-category="终端" data-industry="半导体/核心硬件" data-company="Apple">Apple Vision Pro<br><span class="description">苹果首款空间计算设备，开启混合现实新时代。</span></div>
            <div class="ongoing" data-stage="3" data-category="传输" data-industry="网络/标准" data-company="光通信行业">800G 光模块<br><span class="description">满足AI和机器学习集群对极端带宽的需求。</span></div>
            <div class="ongoing" data-stage="3" data-category="传输" data-industry="网络/标准" data-company="PCI-SIG">PCIe 6.0<br><span class="description">64 GT/s的PCI Express标准，为AI加速器提供更高带宽。</span></div>
            <div class="ongoing" data-stage="3" data-category="服务" data-industry="半导体/核心硬件" data-company="Google">Google TPU v4<br><span class="description">谷歌的第四代TPU，用于训练大型AI模型。</span></div>
            <div class="ongoing" data-stage="3" data-category="服务" data-industry="半导体/核心硬件" data-company="Intel">Intel Gaudi 3<br><span class="description">专为AI训练和推理设计的加速器，挑战Nvidia。</span></div>
            <div class="ongoing" data-stage="3" data-category="服务" data-industry="量子计算" data-company="Google">Google Sycamore<br><span class="description">实现量子霸权的重要里程碑，展示了量子计算的潜力。</span></div>
        </div>
    </div>

<!-- 2022 -->
    <div class="year-bar">
        <div class="year-label">2022</div>
        <div class="step">
            <div class="ongoing active" data-stage="3" data-category="终端" data-industry="半导体/核心硬件" data-company="Apple">Apple M2<br><span class="description">苹果的第二代自研SoC，用于MacBook Air和Pro。</span></div>
            <div class="ongoing" data-stage="3" data-category="终端" data-industry="半导体/核心硬件" data-company="Intel">Intel Arc GPU<br><span class="description">英特尔重返独立显卡市场的首款产品。</span></div>
            <div class="ongoing" data-stage="3" data-category="传输" data-industry="网络/标准" data-company="CXL Consortium">CXL<br><span class="description">一种开放的互连协议，用于连接CPU、内存和加速器。</span></div>
            <div class="ongoing" data-stage="3" data-category="传输" data-industry="网络/标准" data-company="JEDEC">HBM3<br><span class="description">第三代高带宽内存，为AI加速器提供超高内存带宽。</span></div>
            <div class="ongoing" data-stage="3" data-category="服务" data-industry="半导体/核心硬件" data-company="Nvidia">Nvidia H100<br><span class="description">基于Hopper架构的GPU，为AI训练和推理提供顶级性能。</span></div>
            <div class="ongoing" data-stage="3" data-category="服务" data-industry="半导体/核心硬件" data-company="AMD">AMD EPYC 9004<br><span class="description">基于Zen 4架构的服务器CPU，支持DDR5和PCIe 5.0。</span></div>
        </div>
    </div>

<!-- 2021 -->
<div class="year-bar">
    <div class="year-label">2021</div>
    <div class="step">
        <div class="ongoing active" data-stage="3" data-category="终端" data-industry="半导体/核心硬件" data-company="Apple">Apple M1 Max/Pro<br><span class="description">苹果自研的高性能SoC，用于MacBook Pro。</span></div>
        <div class="ongoing" data-stage="3" data-category="传输" data-industry="网络/标准" data-company="JEDEC">DDR5 SDRAM<br><span class="description">新一代内存标准，提供了更高的数据传输速率。</span></div>
        <div class="ongoing" data-stage="3" data-category="服务" data-industry="半导体/核心硬件" data-company="Google">Google TPU v4i<br><span class="description">谷歌专为AI推理设计的芯片。</span></div>
    </div>
</div>

<!-- 2020 -->
<div class="year-bar">
    <div class="year-label">2020</div>
    <div class="step">
        <div class="ongoing active" data-stage="3" data-category="终端" data-industry="软件/服务/生态" data-company="OpenAI">GPT-3<br><span class="description">拥有1750亿参数的语言模型，展示了AI生成内容的巨大潜力。</span></div>
        <div class="ongoing" data-stage="3" data-category="传输" data-industry="网络/标准" data-company="3GPP">5G<br><span class="description">第五代移动通信技术，提供更高的带宽和更低的时延。</span></div>
        <div class="ongoing" data-stage="3" data-category="服务" data-industry="半导体/核心硬件" data-company="Nvidia">Nvidia A100<br><span class="description">专为AI、数据分析和高性能计算设计的GPU。</span></div>
    </div>
</div>

<!-- 2019 -->
<div class="year-bar">
    <div class="year-label">2019</div>
    <div class="step">
        <div class="ongoing active" data-stage="3" data-category="终端" data-industry="半导体/核心硬件" data-company="AMD">AMD Ryzen 3000<br><span class="description">采用7nm工艺和Zen 2架构，在PC市场与英特尔展开激烈竞争。</span></div>
        <div class="ongoing" data-stage="3" data-category="传输" data-industry="网络/标准" data-company="IEEE">Wi-Fi 6<br><span class="description">新一代Wi-Fi标准，提升了速度、容量和效率。</span></div>
        <div class="ongoing" data-stage="3" data-category="服务" data-industry="半导体/核心硬件" data-company="Google">Google Edge TPU<br><span class="description">谷歌为在边缘设备上运行AI而设计的芯片。</span></div>
    </div>
</div>

<!-- 2018 -->
<div class="year-bar">
    <div class="year-label">2018</div>
    <div class="step">
        <div class="ongoing active" data-stage="3" data-category="终端" data-industry="半导体/核心硬件" data-company="Apple">iPhone XS<br><span class="description">搭载A12 Bionic芯片，进一步提升了移动AI性能。</span></div>
        <div class="ongoing" data-stage="3" data-category="服务" data-industry="软件/服务/生态" data-company="Amazon">AWS Nitro<br><span class="description">AWS的虚拟化硬件，提升了云服务器的性能和安全性。</span></div>
        <div class="ongoing" data-stage="3" data-category="服务" data-industry="半导体/核心硬件" data-company="Nvidia">Nvidia Turing<br><span class="description">引入实时光线追踪（RTX）和DLSS技术。</span></div>
    </div>
</div>

<!-- 2017 -->
<div class="year-bar">
    <div class="year-label">2017</div>
    <div class="step">
        <div class="ongoing active" data-stage="3" data-category="终端" data-industry="半导体/核心硬件" data-company="Apple">iPhone X<br><span class="description">引入Face ID和神经引擎，推动了移动AI应用。</span></div>
        <div class="ongoing" data-stage="3" data-category="服务" data-industry="半导体/核心硬件" data-company="Google">Google TPU v2<br><span class="description">谷歌的第二代TPU，可用于训练和推理。</span></div>
        <div class="ongoing" data-stage="3" data-category="服务" data-industry="半导体/核心硬件" data-company="Nvidia">Nvidia Tesla V100<br><span class="description">引入Tensor Core，极大地加速了AI训练。</span></div>
    </div>
</div>

<!-- 2016 -->
<div class="year-bar">
    <div class="year-label">2016</div>
    <div class="step">
        <div class="ongoing active" data-stage="3" data-category="终端" data-industry="软件/服务/生态" data-company="DeepMind/Google">AlphaGo<br><span class="description">击败了世界围棋冠军，是AI发展史上的里程碑。</span></div>
        <div class="ongoing" data-stage="3" data-category="传输" data-industry="网络/标准" data-company="光通信行业">光模块 (QSFP28)<br><span class="description">成为100G数据中心网络的主流方案。</span></div>
        <div class="ongoing" data-stage="3" data-category="服务" data-industry="半导体/核心硬件" data-company="Nvidia">Nvidia DGX-1<br><span class="description">第一台专为深度学习设计的“AI超级计算机”。</span></div>
    </div>
</div>

<!-- 2015 -->
<div class="year-bar">
    <div class="year-label">2015</div>
    <div class="step">
        <div class="ongoing active" data-stage="3" data-category="终端" data-industry="半导体/核心硬件" data-company="Apple">Apple Watch<br><span class="description">苹果进入可穿戴设备市场，定义了现代智能手表。</span></div>
        <div class="ongoing" data-stage="3" data-category="终端" data-industry="软件/服务/生态" data-company="Amazon">Amazon Echo<br><span class="description">内置Alexa语音助手，开创了智能音箱市场。</span></div>
        <div class="ongoing" data-stage="3" data-category="服务" data-industry="半导体/核心硬件" data-company="Google">Google TPU v1<br><span class="description">谷歌为加速AI计算而设计的专用芯片，首次用于AlphaGo。</span></div>
    </div>
</div>

<!-- 2014 -->
<div class="year-bar">
    <div class="year-label">2014</div>
    <div class="step">
        <div class="ongoing" data-stage="3" data-category="服务" data-industry="软件/服务/生态" data-company="Amazon">AWS Lambda<br><span class="description">开创了无服务器计算模式，开发者无需管理服务器。</span></div>
        <div class="ongoing" data-stage="3" data-category="服务" data-industry="半导体/核心硬件" data-company="Nvidia">Nvidia Maxwell<br><span class="description">Nvidia推出的高效能GPU架构，用于游戏和计算。</span></div>
        <div class="ongoing active" data-stage="3" data-category="终端" data-industry="半导体/核心硬件" data-company="Intel">Intel Broadwell<br><span class="description">英特尔首次采用14nm工艺的处理器。</span></div>
    </div>
</div>

<!-- 2013 -->
<div class="year-bar">
    <div class="year-label">2013</div>
    <div class="step">
        <div class="ongoing active" data-stage="3" data-category="终端" data-industry="半导体/核心硬件" data-company="Apple">iPhone 5S<br><span class="description">首款搭载指纹识别和64位处理器的智能手机。</span></div>
        <div class="ongoing" data-stage="3" data-category="终端" data-industry="半导体/核心硬件" data-company="Sony">PlayStation 4<br><span class="description">索尼的第八代游戏主机，全球销量突破1.1亿台。</span></div>
        <div style="flex:1;"></div>
    </div>
</div>

<!-- 2012 -->
<div class="year-bar">
    <div class="year-label">2012</div>
    <div class="step">
        <div class="ongoing active" data-stage="3" data-category="终端" data-industry="半导体/核心硬件" data-company="Raspberry Pi Foundation">Raspberry Pi<br><span class="description">一款极低成本的单板计算机，推动了创客运动。</span></div>
        <div class="ongoing" data-stage="3" data-category="服务" data-industry="软件/服务/生态" data-company="Nvidia">CUDA 5<br><span class="description">Nvidia的并行计算平台，使GPU能用于通用科学计算。</span></div>
        <div class="ongoing" data-stage="3" data-category="服务" data-industry="科研/教育" data-company="Hinton团队">AlexNet<br><span class="description">在ImageNet竞赛中取得突破，引爆了深度学习革命。</span></div>
    </div>
</div>

<!-- 2011 -->
<div class="year-bar">
    <div class="year-label">2011</div>
    <div class="step">
        <div class="ongoing" data-stage="3" data-category="传输" data-industry="网络/标准" data-company="USB-IF">USB 3.0<br><span class="description">传输速度达到5Gbps，是USB 2.0的十倍。</span></div>
        <div class="ongoing" data-stage="3" data-category="服务" data-industry="软件/服务/生态" data-company="IBM">Watson<br><span class="description">IBM的AI系统，在智力竞赛节目中击败人类冠军。</span></div>
        <div style="flex:1;"></div>
    </div>
</div>

<!-- 2010 -->
<div class="year-bar">
    <div class="year-label">2010</div>
    <div class="step">
        <div class="ongoing active" data-stage="3" data-category="终端" data-industry="半导体/核心硬件" data-company="Apple">iPad<br><span class="description">苹果开创了平板电脑这一全新的产品类别。</span></div>
        <div class="ongoing" data-stage="3" data-category="服务" data-industry="半导体/核心硬件" data-company="Intel">Intel Westmere<br><span class="description">英特尔推出的首批6核服务器处理器。</span></div>
        <div style="flex:1;"></div>
    </div>
</div>

<!-- 2008 -->
<div class="year-bar">
    <div class="year-label">2008</div>
    <div class="step">
        <div class="ongoing active" data-stage="2" data-category="终端" data-industry="软件/服务/生态" data-company="Apple">App Store<br><span class="description">苹果为iPhone推出的应用商店，创造了新的软件分发模式。</span></div>
        <div class="fail" data-stage="2" data-category="终端" data-industry="半导体/核心硬件" data-company="Nokia">Nokia N97<br><span class="description">诺基亚在智能手机时代的挣扎，未能跟上市场变化。</span></div>
        <div class="ongoing" data-stage="2" data-category="终端" data-industry="半导体/核心硬件" data-company="Intel">Intel Atom<br><span class="description">专为上网本和低功耗设备设计的处理器。</span></div>
    </div>
</div>

<!-- 2007 -->
<div class="year-bar">
    <div class="year-label">2007</div>
    <div class="step">
        <div class="ongoing active" data-stage="2" data-category="终端" data-industry="半导体/核心硬件" data-company="Apple">iPhone<br><span class="description">重新定义了智能手机，开启了移动互联网时代。</span></div>
        <div class="ongoing" data-stage="2" data-category="服务" data-industry="软件/服务/生态" data-company="Amazon">Amazon S3/EC2<br><span class="description">AWS推出的云存储和云计算服务，开创了IaaS模式。</span></div>
        <div class="success" data-stage="2" data-category="服务" data-industry="半导体/核心硬件" data-company="AMD">AMD Phenom<br><span class="description">AMD推出的第一款原生四核处理器。</span></div>
    </div>
</div>

<!-- 2006 -->
<div class="year-bar">
    <div class="year-label">2006</div>
    <div class="step">
        <div class="ongoing active" data-stage="2" data-category="终端" data-industry="半导体/核心硬件" data-company="Apple">MacBook Pro<br><span class="description">苹果转向使用英特尔处理器，提升了Mac的性能。</span></div>
        <div class="ongoing" data-stage="2" data-category="传输" data-industry="软件/服务/生态" data-company="Google">YouTube CDN<br><span class="description">谷歌建立的全球内容分发网络，加速了视频加载。</span></div>
        <div class="ongoing" data-stage="2" data-category="服务" data-industry="网络/标准" data-company="Cisco">Cisco CRS-1<br><span class="description">一款强大的核心路由器，用于支撑互联网骨干网。</span></div>
    </div>
</div>

<!-- 2005 -->
<div class="year-bar">
    <div class="year-label">2005</div>
    <div class="step">
        <div class="success active" data-stage="2" data-category="终端" data-industry="软件/服务/生态" data-company="Google">YouTube<br><span class="description">成为全球最大的在线视频分享平台。</span></div>
        <div class="success" data-stage="2" data-category="服务" data-industry="软件/服务/生态" data-company="Google">MapReduce<br><span class="description">谷歌开发的大数据处理模型，启发了Hadoop等项目。</span></div>
        <div class="success" data-stage="2" data-category="服务" data-industry="半导体/核心硬件" data-company="IBM">BladeCenter<br><span class="description">IBM推出的刀片服务器，提高了数据中心的密度和效率。</span></div>
    </div>
</div>

<!-- 2004 -->
<div class="year-bar">
    <div class="year-label">2004</div>
    <div class="step">
        <div class="success" data-stage="2" data-category="传输" data-industry="网络/标准" data-company="光通信行业">光模块 (SFP+)<br><span class="description">使10G以太网在数据中心中得以普及。</span></div>
        <div class="success" data-stage="2" data-category="服务" data-industry="半导体/核心硬件" data-company="AMD">AMD64<br><span class="description">AMD推出的64位扩展指令集，被英特尔采纳并成为行业标准。</span></div>
        <div style="flex:1;"></div>
    </div>
</div>

<!-- 2003 -->
<div class="year-bar">
    <div class="year-label">2003</div>
    <div class="step">
        <div class="success" data-stage="2" data-category="服务" data-industry="半导体/核心硬件" data-company="AMD">Opteron<br><span class="description">AMD推出的首款x86-64服务器处理器，挑战英特尔。</span></div>
        <div style="flex:2;"></div>
    </div>
</div>

<!-- 2002 -->
<div class="year-bar">
    <div class="year-label">2002</div>
    <div class="step">
        <div class="success" data-stage="2" data-category="传输" data-industry="网络/标准" data-company="SATA-IO">SATA<br><span class="description">取代了PATA，成为连接硬盘和光驱的新标准。</span></div>
        <div style="flex:2;"></div>
    </div>
</div>

<!-- 2001 -->
<div class="year-bar">
    <div class="year-label">2001</div>
    <div class="step">
        <div class="success active" data-stage="2" data-category="终端" data-industry="半导体/核心硬件" data-company="Apple">iPod<br><span class="description">彻底改变了音乐产业和人们听音乐的方式。</span></div>
        <div class="success" data-stage="2" data-category="传输" data-industry="网络/标准" data-company="IEEE">1 GbE普及<br><span class="description">千兆以太网成为企业和家庭网络的新标准。</span></div>
        <div class="fail" data-stage="2" data-category="服务" data-industry="半导体/核心硬件" data-company="Intel">Itanium IA64<br><span class="description">英特尔试图取代x86架构的努力，但最终失败。</span></div>
    </div>
</div>

<!-- 2000 -->
<div class="year-bar">
    <div class="year-label">2000</div>
    <div class="step">
        <div class="success active" data-stage="2" data-category="终端" data-industry="半导体/核心硬件" data-company="Sony">PlayStation 2<br><span class="description">有史以来最畅销的家用游戏主机。</span></div>
        <div class="success" data-stage="2" data-category="传输" data-industry="网络/标准" data-company="USB-IF">USB 2.0<br><span class="description">大幅提升了数据传输速度，成为主流接口标准。</span></div>
        <div style="flex:1;"></div>
    </div>
</div>

<!-- 1999 -->
<div class="year-bar">
    <div class="year-label">1999</div>
    <div class="step">
        <div class="success active" data-stage="2" data-category="终端" data-industry="半导体/核心硬件" data-company="Nvidia">GeForce 256<br><span class="description">Nvidia推出的第一款GPU，将图形处理能力提升到新高度。</span></div>
        <div class="success" data-stage="2" data-category="传输" data-industry="网络/标准" data-company="Wi-Fi Alliance">Wi-Fi 802.11b<br><span class="description">第一个被广泛采用的Wi-Fi标准，开启了无线互联网时代。</span></div>
        <div class="success" data-stage="2" data-category="服务" data-industry="半导体/核心硬件" data-company="AMD">AMD Athlon<br><span class="description">第一款在消费级市场突破1GHz时钟速度的处理器。</span></div>
    </div>
</div>

<!-- 1997 -->
<div class="year-bar">
    <div class="year-label">1997</div>
    <div class="step">
        <div class="success active" data-stage="2" data-category="终端" data-industry="半导体/核心硬件" data-company="Intel">Pentium II<br><span class="description">引入了MMX技术，增强了多媒体处理能力。</span></div>
        <div class="success" data-stage="2" data-category="终端" data-industry="网络/标准" data-company="DVD Forum">DVD<br><span class="description">取代了VCD和录像带，成为主要的视频和软件分发介质。</span></div>
        <div class="success" data-stage="2" data-category="传输" data-industry="半导体/核心硬件" data-company="Intel">AGP<br><span class="description">专为显卡设计的加速图形端口，提升了3D游戏性能。</span></div>
    </div>
</div>

<!-- 1996 -->
<div class="year-bar">
    <div class="year-label">1996</div>
    <div class="step">
        <div class="success" data-stage="2" data-category="传输" data-industry="网络/标准" data-company="USB-IF">USB 1.0<br><span class="description">一个通用的外部设备连接标准，简化了PC配件的使用。</span></div>
        <div style="flex:2;"></div>
    </div>
</div>

<!-- 1995 -->
<div class="year-bar">
    <div class="year-label">1995</div>
    <div class="step">
        <div class="success active" data-stage="2" data-category="终端" data-industry="软件/服务/生态" data-company="Microsoft">Windows 95<br><span class="description">微软的里程碑式操作系统，带来了开始菜单和任务栏。</span></div>
        <div class="fail" data-stage="2" data-category="终端" data-industry="软件/服务/生态" data-company="Netscape">Netscape Navigator<br><span class="description">早期的主要网页浏览器，但在与IE的竞争中失败。</span></div>
        <div class="success" data-stage="2" data-category="服务" data-industry="半导体/核心硬件" data-company="Intel">Pentium Pro<br><span class="description">第一款专为32位服务器和工作站设计的x86处理器。</span></div>
    </div>
</div>

<!-- 1994 -->
<div class="year-bar">
    <div class="year-label">1994</div>
    <div class="step">
        <div class="success active" data-category="终端" data-industry="半导体/核心硬件" data-company="Sony">PlayStation<br><span class="description">索尼的第一款游戏主机，开启了3D游戏时代。</span></div>
        <div style="flex:2;"></div>
    </div>
</div>

<!-- 1993 -->
<div class="year-bar">
    <div class="year-label">1993</div>
    <div class="step">
        <div class="success active" data-category="终端" data-industry="半导体/核心硬件" data-company="Intel">Pentium<br><span class="description">英特尔的第五代微处理器，极大地提升了PC性能。</span></div>
        <div class="success" data-category="终端" data-industry="科研/教育" data-company="NCSA">Mosaic<br><span class="description">第一款流行的图形化网页浏览器，推动了WWW的普及。</span></div>
        <div class="success" data-category="服务器" data-industry="半导体/核心硬件" data-company="Nvidia">Nvidia成立<br><span class="description">后来成为图形处理器（GPU）市场的领导者。</span></div>
    </div>
</div>

<!-- 1991 -->
<div class="year-bar">
    <div class="year-label">1991</div>
    <div class="step">
        <div class="success" data-category="服务器" data-industry="软件/服务/生态" data-company="Linus Torvalds">Linux Kernel<br><span class="description">一个开源的操作系统内核，成为服务器和安卓系统的基础。</span></div>
        <div style="flex:2;"></div>
    </div>
</div>

<!-- 1989 -->
<div class="year-bar">
    <div class="year-label">1989</div>
    <div class="step">
        <div class="fail active" data-category="终端" data-industry="软件/服务/生态" data-company="NeXT">NeXTcube<br><span class="description">乔布斯离开苹果后开发的高端工作站，其软件技术后被苹果采用。</span></div>
        <div class="success" data-category="传输" data-industry="科研/教育" data-company="CERN">WWW<br><span class="description">由Tim Berners-Lee发明，使互联网能够被大众访问。</span></div>
        <div class="fail" data-category="服务器" data-industry="半导体/核心硬件" data-company="Sun">Sun SPARC<br><span class="description">在90年代主导了工作站和服务器市场。</span></div>
    </div>
</div>

<!-- 1988 -->
<div class="year-bar">
    <div class="year-label">1988</div>
    <div class="step">
        <div class="success" data-category="服务器" data-industry="软件/服务/生态" data-company="UC Berkeley">RAID<br><span class="description">通过组合多个磁盘，提高了数据存储的可靠性和性能。</span></div>
        <div style="flex:2;"></div>
    </div>
</div>

<!-- 1985 -->
<div class="year-bar">
    <div class="year-label">1985</div>
    <div class="step">
        <div class="fail active" data-category="终端" data-industry="半导体/核心硬件" data-company="Commodore">Amiga 1000<br><span class="description">拥有先进的多媒体功能，但在市场竞争中失败。</span></div>
        <div class="success" data-category="传输" data-industry="科研/教育" data-company="NSF">NSFNET<br><span class="description">连接了美国的大学和研究中心，成为互联网的骨干网。</span></div>
        <div class="success" data-category="服务器" data-industry="半导体/核心硬件" data-company="Intel">Intel 80386<br><span class="description">第一款32位x86处理器，带来了多任务处理能力。</span></div>
    </div>
</div>

<!-- 1984 -->
<div class="year-bar">
    <div class="year-label">1984</div>
    <div class="step">
        <div class="success active" data-category="终端" data-industry="半导体/核心硬件" data-company="Apple">Macintosh<br><span class="description">成功地将图形用户界面和鼠标推广给大众。</span></div>
        <div class="success" data-category="服务器" data-industry="半导体/核心硬件" data-company="MIPS">MIPS R2000<br><span class="description">早期成功的RISC（精简指令集）处理器之一。</span></div>
        <div style="flex:1;"></div>
    </div>
</div>

<!-- 1983 -->
<div class="year-bar">
    <div class="year-label">1983</div>
    <div class="step">
        <div class="fail active" data-category="终端" data-industry="半导体/核心硬件" data-company="Apple">Lisa<br><span class="description">首批采用图形用户界面的商用电脑之一，但价格昂贵。</span></div>
        <div class="success" data-category="传输" data-industry="网络/标准" data-company="ARPANET">TCP/IP<br><span class="description">成为ARPANET的标准协议，是现代互联网的基石。</span></div>
        <div class="success" data-category="服务器" data-industry="半导体/核心硬件" data-company="IBM">IBM PC XT<br><span class="description">首次将硬盘作为标准配置，提升了PC的实用性。</span></div>
    </div>
</div>

<!-- 1982 -->
<div class="year-bar">
    <div class="year-label">1982</div>
    <div class="step">
        <div class="success active" data-category="终端" data-industry="半导体/核心硬件" data-company="Commodore">Commodore 64<br><span class="description">历史上最畅销的单一计算机型号。</span></div>
        <div style="flex:2;"></div>
    </div>
</div>

<!-- 1981 -->
<div class="year-bar">
    <div class="year-label">1981</div>
    <div class="step">
        <div class="success active" data-stage="1" data-category="终端" data-industry="半导体/核心硬件" data-company="IBM">IBM PC 5150<br><span class="description">为个人电脑制定了行业标准，让电脑走入办公室和家庭。</span></div>
        <div class="success" data-stage="1" data-category="终端" data-industry="软件/服务/生态" data-company="Microsoft">MS-DOS<br><span class="description">IBM PC的标准操作系统，统治了PC市场多年。</span></div>
        <div class="success" data-stage="1" data-category="服务" data-industry="半导体/核心硬件" data-company="Seagate">Shugart ST506<br><span class="description">第一款为个人电脑设计的5.25英寸硬盘。</span></div>
    </div>
</div>

<!-- 1978 -->
<div class="year-bar">
    <div class="year-label">1978</div>
    <div class="step">
        <div class="success" data-stage="1" data-category="服务" data-industry="半导体/核心硬件" data-company="DEC">VAX-11/780<br><span class="description">32位小型机的代表，广泛用于科研和工程领域。</span></div>
        <div class="success active" data-stage="1" data-category="终端" data-industry="半导体/核心硬件" data-company="Shugart Associates">5.25" 软盘<br><span class="description">成为个人电脑软件和数据交换的主要介质。</span></div>
        <div class="success" data-stage="1" data-category="服务" data-industry="半导体/核心硬件" data-company="Intel">Intel 8086<br><span class="description">x86架构的起点，是现代PC处理器的始祖。</span></div>
    </div>
</div>

<!-- 1977 -->
<div class="year-bar">
    <div class="year-label">1977</div>
    <div class="step">
        <div class="success active" data-stage="1" data-category="终端" data-industry="半导体/核心硬件" data-company="Apple">Apple II<br><span class="description">第一款大规模成功的个人电脑。</span></div>
        <div class="success" data-stage="1" data-category="终端" data-industry="半导体/核心硬件" data-company="Atari">Atari 2600<br><span class="description">定义了家用游戏主机市场，普及了可换卡带的设计。</span></div>
        <div style="flex:1;"></div>
    </div>
</div>

<!-- 1976 -->
<div class="year-bar">
    <div class="year-label">1976</div>
    <div class="step">
        <div class="success active" data-stage="1" data-category="终端" data-industry="半导体/核心硬件" data-company="Apple">Apple I<br><span class="description">苹果公司的第一款产品，主要面向计算机爱好者。</span></div>
        <div class="fail" data-stage="1" data-category="终端" data-industry="半导体/核心硬件" data-company="MITS">Altair 8800<br><span class="description">激发了第一代个人电脑爱好者的热情。</span></div>
        <div class="fail" data-stage="1" data-category="服务" data-industry="半导体/核心硬件" data-company="Cray Research">Cray-1<br><span class="description">定义了超级计算机的形态和性能。</span></div>
    </div>
</div>

<!-- 1973 -->
<div class="year-bar">
    <div class="year-label">1973</div>
    <div class="step">
        <div class="success" data-stage="1" data-category="传输" data-industry="科研/教育" data-company="Xerox PARC">以太网<br><span class="description">定义了有线局域网的标准，至今仍在使用。</span></div>
        <div style="flex:2;"></div>
    </div>
</div>

<!-- 1971 -->
<div class="year-bar">
    <div class="year-label">1971</div>
    <div class="step">
        <div class="success" data-stage="1" data-category="服务" data-industry="半导体/核心硬件" data-company="Intel">Intel 4004<br><span class="description">第一款商用微处理器，将整个CPU集成到单颗芯片上。</span></div>
        <div class="success active" data-stage="1" data-category="终端" data-industry="半导体/核心硬件" data-company="IBM">8"软盘<br><span class="description">第一个便携式、可重复写入的存储介质。</span></div>
        <div class="fail" data-stage="1" data-category="服务" data-industry="半导体/核心硬件" data-company="DEC">DEC PDP-11<br><span class="description">非常成功的小型机，广泛用于各种应用。</span></div>
    </div>
</div>

<!-- 1969 -->
<div class="year-bar">
    <div class="year-label">1969</div>
    <div class="step">
        <div class="fail" data-stage="1" data-category="传输" data-industry="网络/标准" data-company="BBN">ARPANET IMP<br><span class="description">互联网的前身，用于连接不同研究机构的计算机。</span></div>
        <div class="success" data-stage="1" data-category="服务" data-industry="软件/服务/生态" data-company="AT&T/Bell">Unix<br><span class="description">一个强大且可移植的操作系统，影响了后来的许多OS。</span></div>
        <div class="success" data-stage="1" data-category="服务" data-industry="半导体/核心硬件" data-company="Honeywell">Honeywell 316<br><span class="description">作为IMP的一部分，是早期互联网的关键硬件。</span></div>
    </div>
</div>

<!-- 1964 -->
<div class="year-bar">
    <div class="year-label">1964</div>
    <div class="step">
        <div class="success" data-stage="1" data-category="服务" data-industry="半导体/核心硬件" data-company="IBM">IBM System/360<br><span class="description">首个实现了指令集架构兼容的计算机系列。</span></div>
        <div class="success" data-stage="1" data-category="传输" data-industry="半导体/核心硬件" data-company="IBM">9-track磁带<br><span class="description">成为行业标准的数据存储和交换格式。</span></div>
        <div class="success active" data-stage="1" data-category="终端" data-industry="科研/教育" data-company="Dartmouth">BASIC语言<br><span class="description">一种易于学习的编程语言，推动了个人计算的普及。</span></div>
    </div>
</div>

<!-- 1960 -->
<div class="year-bar">
    <div class="year-label">1960</div>
    <div class="step">
        <div class="success" data-stage="1" data-category="服务" data-industry="半导体/核心硬件" data-company="DEC">DEC PDP-1<br><span class="description">开创了小型机市场，让更多组织能用上计算机。</span></div>
        <div class="success" data-stage="1" data-category="服务" data-industry="软件/服务/生态" data-company="CODASYL">COBOL<br><span class="description">用于商业数据处理的主要编程语言。</span></div>
        <div class="success" data-stage="1" data-category="服务" data-industry="科研/教育" data-company="John McCarthy">LISP<br><span class="description">人工智能研究中长期使用的核心编程语言。</span></div>
    </div>
</div>

<!-- 1958 -->
<div class="year-bar">
    <div class="year-label">1958</div>
    <div class="step">
        <div class="success" data-stage="1" data-category="服务" data-industry="半导体/核心硬件" data-company="Texas Instruments">集成电路<br><span class="description">将多个晶体管集成到一块芯片上，是微处理器的前身。</span></div>
        <div style="flex:2;"></div>
    </div>
</div>

<!-- 1957 -->
<div class="year-bar">
    <div class="year-label">1957</div>
    <div class="step">
        <div class="success" data-stage="1" data-category="服务" data-industry="软件/服务/生态" data-company="IBM">FORTRAN<br><span class="description">最早的高级编程语言之一，主要用于科学计算。</span></div>
        <div style="flex:2;"></div>
    </div>
</div>

<!-- 1956 -->
<div class="year-bar">
    <div class="year-label">1956</div>
    <div class="step">
        <div class="success" data-stage="1" data-category="服务" data-industry="半导体/核心硬件" data-company="IBM">IBM RAMAC<br><span class="description">第一台使用磁盘驱动器进行数据存储的计算机。</span></div>
        <div class="success" data-stage="1" data-category="传输" data-industry="网络/标准" data-company="Bell/AT&T">海底电话电缆<br><span class="description">首次实现了跨大西洋的即时电话通信。</span></div>
        <div class="success" data-stage="1" data-category="服务" data-industry="科研/教育" data-company="MIT Lincoln Lab">TX-0<br><span class="description">第一批完全使用晶体管的计算机之一。</span></div>
    </div>
</div>

<!-- 1951 -->
<div class="year-bar">
    <div class="year-label">1951</div>
    <div class="step">
        <div class="success" data-stage="1" data-category="服务" data-industry="半导体/核心硬件" data-company="Remington Rand">UNIVAC I<br><span class="description">第一台在美国制造的商用计算机。</span></div>
        <div class="success" data-stage="1" data-category="服务" data-industry="半导体/核心硬件" data-company="ERA/Remington">磁带驱动器<br><span class="description">用于数据存储和备份，取代了打孔卡。</span></div>
        <div class="success" data-stage="1" data-category="服务" data-industry="科研/教育" data-company="MIT">Whirlwind I<br><span class="description">第一台能够实时处理数据的计算机。</span></div>
    </div>
</div>

<!-- 1947 -->
<div class="year-bar">
    <div class="year-label">1947</div>
    <div class="step">
        <div class="success" data-stage="1" data-category="服务" data-industry="科研/教育" data-company="Bell Labs">晶体管<br><span class="description">取代了真空管，是所有现代电子设备的基础。</span></div>
        <div style="flex:2;"></div>
    </div>
</div>

<!-- 1945 -->
    <div class="year-bar">
        <div class="year-label">1945</div>
        <div class="step">
            <div class="success" data-stage="1" data-category="服务" data-industry="科研/教育" data-company="UPenn">ENIAC<br><span class="description">世界上第一台通用电子计算机，用于弹道计算。</span></div>
            <div class="success" data-stage="1" data-category="服务" data-industry="科研/教育" data-company="John von Neumann">冯·诺依曼架构<br><span class="description">定义了现代计算机的基本结构，至今仍是主流。</span></div>
            <div class="success" data-stage="1" data-category="传输" data-industry="网络/标准" data-company="Bell Labs">电话继电器<br><span class="description">早期电话网络中用于自动连接通话的机械开关。</span></div>
            <div class="success" data-stage="1" data-category="服务" data-industry="半导体/核心硬件" data-company="IBM">Harvard Mark I<br><span class="description">由IBM建造的早期机电计算机，服务于二战。</span></div>
        </div>
    </div>

    <!-- 1943 -->
    <div class="year-bar">
        <div class="year-label">1943</div>
        <div class="step">
            <div class="success" data-stage="1" data-category="服务" data-industry="科研/教育" data-company="British">Colossus<br><span class="description">世界上第一台可编程电子计算机，用于破解德军密码。</span></div>
            <div class="success" data-stage="1" data-category="服务" data-industry="科研/教育" data-company="Bell Labs">SIGSALY<br><span class="description">第一个安全的数字语音加密系统，用于战时通信。</span></div>
            <div style="flex:1;"></div>
        </div>
    </div>

    <!-- 1941 -->
    <div class="year-bar">
        <div class="year-label">1941</div>
        <div class="step">
            <div class="success" data-stage="1" data-category="服务" data-industry="科研/教育" data-company="Konrad Zuse">Z3<br><span class="description">第一台可编程的自动计算机，使用二进制浮点数。</span></div>
            <div style="flex:2;"></div>
        </div>
    </div>

    <!-- 1936 -->
    <div class="year-bar">
        <div class="year-label">1936</div>
        <div class="step">
            <div class="success" data-stage="1" data-category="服务" data-industry="科研/教育" data-company="Alan Turing">图灵机<br><span class="description">理论计算模型，奠定了现代计算机科学的理论基础。</span></div>
            <div style="flex:2;"></div>
        </div>
    </div>

<!--LADDER_CONTENT_END-->
</div>

<p style="text-align:center;font-size:12px;color:#888;margin:40px 0;">
  数据来源：公开资料整理。可自由修改、转发。
</p>

<script>
let currentCategory = '终端';
let currentIndustry = 'All';
let currentCompany = 'All';
let currentStage = 'All';

function filterByCategory(category, clickedButton) {
    currentCategory = category;
    currentIndustry = 'All'; // Reset industry filter
    currentCompany = 'All'; // Reset company filter

    // Update active state on primary tabs
    document.querySelectorAll('.tab-button').forEach(button => button.classList.remove('active'));
    clickedButton.classList.add('active');

    updateSecondaryMenu();
    updateCompanyMenu();
    filterContent();
}

function updateSecondaryMenu() {
    const secondaryTabsContainer = document.getElementById('secondary-tabs');
    secondaryTabsContainer.innerHTML = '';
    const industries = new Set();
    
    document.querySelectorAll(`.step > div[data-category="${currentCategory}"]`).forEach(item => {
        if (item.dataset.industry) {
            industries.add(item.dataset.industry);
        }
    });

    const allButton = document.createElement('button');
    allButton.textContent = 'All';
    allButton.className = 'secondary-button active';
    allButton.onclick = () => filterByIndustry('All', allButton);
    secondaryTabsContainer.appendChild(allButton);

    Array.from(industries).sort().forEach(industry => {
        const button = document.createElement('button');
        button.textContent = industry;
        button.className = 'secondary-button';
        button.onclick = () => filterByIndustry(industry, button);
        secondaryTabsContainer.appendChild(button);
    });
}

function filterByIndustry(industry, clickedButton) {
    currentIndustry = industry;
    currentCompany = 'All'; // Reset company filter when industry changes
    
    // Update active state on secondary tabs
    document.querySelectorAll('.secondary-button').forEach(button => button.classList.remove('active'));
    clickedButton.classList.add('active');

    updateCompanyMenu(); // Update company menu when industry changes
    filterContent();
}

function updateCompanyMenu() {
    const companyTabsContainer = document.getElementById('company-tabs');
    companyTabsContainer.innerHTML = '';
    const companies = new Set();

    document.querySelectorAll(`.step > div`).forEach(item => {
        // Only add company if it matches current category and industry, or if no company is selected yet
        const isCategoryMatch = item.dataset.category === currentCategory;
        const isIndustryMatch = currentIndustry === 'All' || item.dataset.industry === currentIndustry;

        if (isCategoryMatch && isIndustryMatch && item.dataset.company) {
            companies.add(item.dataset.company);
        }
    });

    const allButton = document.createElement('button');
    allButton.textContent = 'All Companies';
    allButton.className = 'company-button active';
    allButton.onclick = () => filterByCompany('All', allButton);
    companyTabsContainer.appendChild(allButton);

    Array.from(companies).sort().forEach(company => {
        const button = document.createElement('button');
        button.textContent = company;
        button.className = 'company-button';
        button.onclick = () => filterByCompany(company, button);
        companyTabsContainer.appendChild(button);
    });
}

function filterByStage(stage, clickedButton) {
    currentStage = stage;

    // Update active state on stage tabs
    document.querySelectorAll('.stage-button').forEach(button => button.classList.remove('active'));
    clickedButton.classList.add('active');

    filterContent();
}

function filterByCompany(company, clickedButton) {
    currentCompany = company;

    // Clear active state on primary and secondary tabs when a company is selected
    document.querySelectorAll('.tab-button').forEach(button => button.classList.remove('active'));
    document.querySelectorAll('.secondary-button').forEach(button => button.classList.remove('active'));

    // Update active state on company tabs
    document.querySelectorAll('.company-button').forEach(button => button.classList.remove('active'));
    clickedButton.classList.add('active');

    filterContent();
}

function filterContent() {
    document.querySelectorAll('.year-bar').forEach(yearBar => {
        let hasVisibleChild = false;
        yearBar.querySelectorAll('.step > div').forEach(item => {
            let shouldBeActive = false;

            if (currentCompany !== 'All') {
                // If a specific company is selected, show all its products regardless of category/industry
                shouldBeActive = item.dataset.company === currentCompany;
            } else {
                // Otherwise, filter by category and industry
                const isCategoryMatch = item.dataset.category === currentCategory;
                const isIndustryMatch = currentIndustry === 'All' || item.dataset.industry === currentIndustry;
                const isStageMatch = currentStage === 'All' || !item.dataset.stage || item.dataset.stage === currentStage;
                shouldBeActive = isCategoryMatch && isIndustryMatch && isStageMatch;
            }

            if (shouldBeActive) {
                item.classList.add('active');
                hasVisibleChild = true;
            } else {
                item.classList.remove('active');
            }
        });

        if (hasVisibleChild) {
            yearBar.style.display = 'flex';
        } else {
            yearBar.style.display = 'none';
        }
    });
}

// Initial load
document.addEventListener('DOMContentLoaded', () => {
    // Reset all filters initially
    currentCategory = '终端';
    currentIndustry = 'All';
    currentCompany = 'All';
    currentStage = 'All';

    // Manually set the active state for the default category button
    const defaultCategoryButton = document.querySelector('.tab-button[onclick*="filterByCategory(\'终端\')"]');
    if (defaultCategoryButton) {
        defaultCategoryButton.classList.add('active');
    }

    const allStagesButton = document.querySelector('.stage-button');
    if(allStagesButton) {
        allStagesButton.classList.add('active');
    }

    // Update secondary and company menus based on the default category
    updateSecondaryMenu();
    updateCompanyMenu();
    filterContent();
});
</script>

</body>
</html>
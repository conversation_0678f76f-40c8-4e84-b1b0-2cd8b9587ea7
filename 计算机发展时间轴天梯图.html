<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
<title>计算机发展时间轴天梯图 - 三阶段硬件演进</title>
<style>
    body{
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
        margin:0;
        padding: 8px;
        background:#111;
        color:#eee;
        font-size: 16px;
        line-height: 1.5;
        -webkit-text-size-adjust: 100%;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
    h1{text-align:center;margin:15px 0;color:#fff;font-size:1.8em;font-weight:600;}
    .subtitle{text-align:center;margin:8px 0;color:#ccc;font-size:15px;}
    
    .phase-description{max-width:1200px;margin:15px auto;padding:12px;background:#1a1a1a;border-radius:8px;display:flex;justify-content:space-around;flex-wrap:wrap;}
    .phase{flex:1;padding:0 10px;border-left:1px solid #444;min-width:280px;}
    .phase:first-child{border-left:none;}
    .phase h3{font-size:15px;color:#eee;margin-top:0;margin-bottom:8px;font-weight:600;}
    .phase p{font-size:13px;color:#ccc;line-height:1.4;margin:5px 0;}
    .phase .era1{color:#FF6B6B;}
    .phase .era2{color:#4ECDC4;}
    .phase .era3{color:#45B7D1;}
    
    .tabs{text-align:center;margin:15px 0;display:flex;justify-content:center;gap:8px;flex-wrap:wrap;padding:0 5px;}
    .tab-group{position:relative;display:inline-block;margin:3px;}
    .tab-button{background:#333;color:#fff;border:1px solid #555;padding:10px 14px;cursor:pointer;border-radius:6px;transition:all 0.3s ease;font-size:15px;font-weight:500;min-height:44px;display:flex;align-items:center;justify-content:center;}
    .tab-button.active{background:#4facfe;border-color:#4facfe;color:#fff;}
    .tab-button:hover{background:#555;border-color:#777;}

    .sub-menu{position:absolute;top:100%;left:0;background:#2a2a2a;border:1px solid #555;border-radius:6px;padding:8px;margin-top:5px;min-width:140px;z-index:1000;box-shadow:0 4px 12px rgba(0,0,0,0.4);}
    .sub-button{display:block;width:100%;background:transparent;color:#ccc;border:none;padding:8px 12px;cursor:pointer;border-radius:4px;margin:2px 0;text-align:left;font-size:14px;transition:all 0.3s ease;min-height:36px;display:flex;align-items:center;}
    .sub-button:hover{background:#444;color:#fff;}
    .sub-button.active{background:#4facfe;color:#fff;}
    
    .legend{margin:20px auto;width:90%;max-width:600px;font-size:13px;display:flex;justify-content:space-around;}
    .legend span{padding:4px 8px;border-radius:4px;margin-right:6px;}
    .legend .era1{background:#FF6B6B;color:#fff;}
    .legend .era2{background:#4ECDC4;color:#fff;}
    .legend .era3{background:#45B7D1;color:#fff;}
    
    .ladder{display:flex;flex-direction:column-reverse;align-items:center;width:100%;}
    .year-bar{width:90%;max-width:1200px;display:flex;flex-direction:column;border-top:1px solid #444;margin-bottom:4px;}
    .year-header{display:flex;width:100%;}
    .year-label{width:80px;flex-shrink:0;text-align:center;font-weight:bold;padding:6px 0;background:#222;}
    .year-events{flex:1;background:#1a1a1a;padding:8px;border-left:1px solid #444;}
    .events-section{margin-bottom:8px;}
    .events-title{font-size:11px;font-weight:bold;margin-bottom:4px;}
    .events-title.success{color:#4CAF50;}
    .events-title.failure{color:#f44336;}
    .events-list{font-size:10px;line-height:1.4;color:#ccc;}
    .step{display:flex;flex:1;height:auto;min-height:70px;overflow:hidden;}
    .step>div{flex:1;display:flex;flex-direction:column;justify-content:center;align-items:center;font-size:12px;padding:8px 4px;border-left:1px solid #333;position:relative;}
    
    .step .era1{background:#2d1b1b;border-left:3px solid #FF6B6B;}
    .step .era2{background:#1b2d2d;border-left:3px solid #4ECDC4;}
    .step .era3{background:#1b1b2d;border-left:3px solid #45B7D1;}

    .step .hardware{background-color:#1a2d1a;}
    .step .software{background-color:#1a1a2d;}
    .step .internet{background-color:#2d1a1a;}
    .step .ai{background-color:#2d1a2d;}

    /* 移动端适配 */
    @media (max-width: 768px) {
        body { padding: 5px; font-size: 15px; }
        h1 { font-size: 1.5em; margin: 10px 0; }
        .subtitle { font-size: 14px; }

        .phase-description {
            flex-direction: column;
            padding: 10px;
            margin: 10px auto;
        }
        .phase {
            border-left: none;
            border-top: 1px solid #444;
            padding: 8px 0;
            min-width: auto;
        }
        .phase:first-child { border-top: none; }
        .phase h3 { font-size: 14px; }
        .phase p { font-size: 12px; }

        .tabs {
            gap: 5px;
            margin: 10px 0;
            padding: 0 3px;
        }
        .tab-button {
            padding: 12px 10px;
            font-size: 14px;
            min-width: 70px;
            flex: 1;
            max-width: 120px;
        }

        .sub-menu {
            min-width: 120px;
            left: 50%;
            transform: translateX(-50%);
        }
        .sub-button {
            font-size: 13px;
            padding: 10px 12px;
        }

        .year-bar { margin: 8px 0; }
        .year-label {
            font-size: 16px;
            padding: 8px 12px;
            min-width: 60px;
        }

        .step { gap: 8px; }
        .step > div {
            padding: 10px;
            min-height: auto;
        }
        .layer-indicator {
            font-size: 11px;
            padding: 3px 6px;
        }
        .hardware-name {
            font-size: 14px;
            margin: 6px 0;
        }
        .description {
            font-size: 12px;
            line-height: 1.4;
        }

        .year-events { margin-top: 8px; }
        .events-section { margin: 6px 0; }
        .events-title {
            font-size: 13px;
            padding: 6px 10px;
        }
        .events-list {
            font-size: 12px;
            padding: 8px 10px;
            line-height: 1.4;
        }
    }

    @media (max-width: 480px) {
        body { font-size: 14px; }
        h1 { font-size: 1.3em; }
        .subtitle { font-size: 13px; }

        .tab-button {
            padding: 10px 8px;
            font-size: 13px;
            min-width: 60px;
        }

        .year-label {
            font-size: 15px;
            padding: 6px 10px;
        }

        .hardware-name { font-size: 13px; }
        .description { font-size: 11px; }
        .events-title { font-size: 12px; }
        .events-list { font-size: 11px; }
    }
    
    .step>div.active{display:flex;}
    .step>div{display:none;}
    .step>div.show-all{display:flex;}
    
    .description{font-size:11px;color:#bbb;margin-top:4px;font-style:italic;}
    .hardware-name{font-weight:bold;color:#fff;margin-bottom:2px;}
    .layer-indicator{position:absolute;top:2px;right:2px;font-size:8px;padding:1px 3px;border-radius:2px;background:#666;color:#fff;}
    
    @media (max-width:1000px){
        .year-bar{width:95%;}
        .step>div{font-size:11px;padding:6px 2px;}
        .phase-description{flex-direction:column;padding:10px;}
        .phase{border-left:none;border-top:1px solid #444;padding:10px 0;}
        .phase:first-child{border-top:none;}
    }
</style>
</head>
<body>
<h1>计算机发展时间轴天梯图</h1>
<div class="subtitle">三阶段技术演进：硬件 × 软件 × 互联网服务 × AI服务</div>

<div class="phase-description">
    <div class="phase">
        <h3><span class="era1">第一阶段: 计算机时代 (约1970s - 1995)</span></h3>
        <p><strong>核心:</strong> 计算的诞生与普及</p>
        <p><strong>① 硬件:</strong> 微处理器、个人电脑、存储设备</p>
        <p><strong>② 软件:</strong> 操作系统、编程语言、应用软件</p>
        <p><strong>③ 互联网服务:</strong> 早期网络协议、电子邮件、文件传输</p>
        <p><strong>④ AI服务:</strong> 专家系统、知识库、早期机器学习</p>
    </div>
    <div class="phase">
        <h3><span class="era2">第二阶段: 互联网时代 (约1995 - 2010)</span></h3>
        <p><strong>核心:</strong> 连接的价值</p>
        <p><strong>① 硬件:</strong> 网络设备、移动设备、多媒体硬件</p>
        <p><strong>② 软件:</strong> 网页浏览器、多媒体软件、移动应用</p>
        <p><strong>③ 互联网服务:</strong> 搜索引擎、电子商务、社交网络</p>
        <p><strong>④ AI服务:</strong> 推荐系统、语音识别、图像处理</p>
    </div>
    <div class="phase">
        <h3><span class="era3">第三阶段: AI与云时代 (约2010 - 至今)</span></h3>
        <p><strong>核心:</strong> 数据的智能</p>
        <p><strong>① 硬件:</strong> AI芯片、云计算硬件、边缘设备</p>
        <p><strong>② 软件:</strong> 深度学习框架、云原生软件、AI应用</p>
        <p><strong>③ 互联网服务:</strong> 云计算平台、流媒体、共享经济</p>
        <p><strong>④ AI服务:</strong> 大语言模型、计算机视觉、自动驾驶</p>
    </div>
</div>

<div class="tabs">
    <div class="tab-group">
        <button class="tab-button active" onclick="toggleSubMenu('hardware', this)">① 硬件</button>
        <div class="sub-menu" id="hardware-submenu" style="display:none;">
            <button class="sub-button active" onclick="filterByIndustry('hardware', 'all', this)">全部</button>
            <button class="sub-button" onclick="filterByIndustry('hardware', 'processor', this)">处理器</button>
            <button class="sub-button" onclick="filterByIndustry('hardware', 'memory', this)">存储设备</button>
            <button class="sub-button" onclick="filterByIndustry('hardware', 'networking', this)">网络设备</button>
            <button class="sub-button" onclick="filterByIndustry('hardware', 'mobile', this)">移动设备</button>
            <button class="sub-button" onclick="filterByIndustry('hardware', 'gaming', this)">游戏硬件</button>
            <button class="sub-button" onclick="filterByIndustry('hardware', 'ai', this)">AI芯片</button>
            <button class="sub-button" onclick="filterByIndustry('hardware', 'iot', this)">物联网</button>
        </div>
    </div>
    <div class="tab-group">
        <button class="tab-button" onclick="toggleSubMenu('software', this)">② 软件</button>
        <div class="sub-menu" id="software-submenu" style="display:none;">
            <button class="sub-button active" onclick="filterByIndustry('software', 'all', this)">全部</button>
            <button class="sub-button" onclick="filterByIndustry('software', 'os', this)">操作系统</button>
            <button class="sub-button" onclick="filterByIndustry('software', 'programming', this)">编程语言</button>
            <button class="sub-button" onclick="filterByIndustry('software', 'application', this)">应用软件</button>
            <button class="sub-button" onclick="filterByIndustry('software', 'database', this)">数据库</button>
            <button class="sub-button" onclick="filterByIndustry('software', 'development', this)">开发工具</button>
            <button class="sub-button" onclick="filterByIndustry('software', 'security', this)">安全软件</button>
            <button class="sub-button" onclick="filterByIndustry('software', 'multimedia', this)">多媒体</button>
        </div>
    </div>
    <div class="tab-group">
        <button class="tab-button" onclick="toggleSubMenu('internet', this)">③ 互联网服务</button>
        <div class="sub-menu" id="internet-submenu" style="display:none;">
            <button class="sub-button active" onclick="filterByIndustry('internet', 'all', this)">全部</button>
            <button class="sub-button" onclick="filterByIndustry('internet', 'search', this)">搜索引擎</button>
            <button class="sub-button" onclick="filterByIndustry('internet', 'ecommerce', this)">电子商务</button>
            <button class="sub-button" onclick="filterByIndustry('internet', 'social', this)">社交网络</button>
            <button class="sub-button" onclick="filterByIndustry('internet', 'cloud', this)">云计算</button>
            <button class="sub-button" onclick="filterByIndustry('internet', 'streaming', this)">流媒体</button>
            <button class="sub-button" onclick="filterByIndustry('internet', 'communication', this)">通信服务</button>
            <button class="sub-button" onclick="filterByIndustry('internet', 'sharing', this)">共享经济</button>
        </div>
    </div>
    <div class="tab-group">
        <button class="tab-button" onclick="toggleSubMenu('ai', this)">④ AI服务</button>
        <div class="sub-menu" id="ai-submenu" style="display:none;">
            <button class="sub-button active" onclick="filterByIndustry('ai', 'all', this)">全部</button>
            <button class="sub-button" onclick="filterByIndustry('ai', 'nlp', this)">自然语言</button>
            <button class="sub-button" onclick="filterByIndustry('ai', 'vision', this)">计算机视觉</button>
            <button class="sub-button" onclick="filterByIndustry('ai', 'speech', this)">语音识别</button>
            <button class="sub-button" onclick="filterByIndustry('ai', 'recommendation', this)">推荐系统</button>
            <button class="sub-button" onclick="filterByIndustry('ai', 'autonomous', this)">自动驾驶</button>
            <button class="sub-button" onclick="filterByIndustry('ai', 'robotics', this)">机器人</button>
            <button class="sub-button" onclick="filterByIndustry('ai', 'expert', this)">专家系统</button>
        </div>
    </div>
</div>

<div class="legend">
    <span class="era1">第一阶段：计算机时代</span>
    <span class="era2">第二阶段：互联网时代</span>
    <span class="era3">第三阶段：AI与云时代</span>
</div>

<div class="ladder">
<!-- 2024 -->
<div class="year-bar">
    <div class="year-header">
        <div class="year-label">2024</div>
        <div class="year-events">
            <div class="events-section">
                <div class="events-title success">重要推动事件</div>
                <div class="events-list">• OpenAI发布GPT-4o，多模态AI能力革命性提升 • Apple Vision Pro正式发售，空间计算时代开启 • 英伟达市值突破3万亿美元，AI芯片需求爆发 • 量子计算商业化加速，IBM推出1000+量子比特处理器</div>
            </div>
            <div class="events-section">
                <div class="events-title" style="color:#4CAF50;">硬件事件</div>
                <div class="events-list">• Apple M4芯片发布，3nm工艺AI性能大幅提升 • 英伟达H200 GPU量产，141GB HBM3e内存 • Wi-Fi 7标准商用，46Gbps理论速度 • DDR5内存普及，8400MT/s高速传输</div>
            </div>
            <div class="events-section">
                <div class="events-title" style="color:#2196F3;">软件事件</div>
                <div class="events-list">• Claude 3.5 Sonnet发布，代码生成能力突破 • Windows 11 24H2更新，集成Copilot AI助手 • iOS 18发布，Apple Intelligence本地AI功能 • 开源大模型Llama 3.1发布，4050亿参数</div>
            </div>
            <div class="events-section">
                <div class="events-title" style="color:#FF9800;">互联网服务</div>
                <div class="events-list">• TikTok全球用户突破15亿，短视频主导内容消费 • 微软Copilot集成Office全家桶，AI办公普及 • AWS推出Bedrock模型服务，云端AI民主化 • Meta推出Threads挑战X(Twitter)</div>
            </div>
            <div class="events-section">
                <div class="events-title" style="color:#9C27B0;">AI服务</div>
                <div class="events-list">• ChatGPT-4o实现实时语音对话，多模态交互突破 • Sora文本生成视频技术震撼发布 • Claude 3.5在编程任务上超越GPT-4 • 自动驾驶FSD v12.3大幅改进，端到端神经网络</div>
            </div>
            <div class="events-section">
                <div class="events-title failure">失败案例</div>
                <div class="events-list">• Meta裁员1.1万人，元宇宙投资遭遇现实挫折 • 英特尔Arc显卡市场表现不佳，游戏性能差距明显 • 多家AI初创公司因资金链断裂倒闭，泡沫破裂 • Cruise自动驾驶出租车服务暂停，安全事故频发</div>
            </div>
        </div>
    </div>
    <div class="step">
        <div class="era3 hardware active" data-category="hardware" data-industry="processor ai">
            <div class="layer-indicator">硬件</div>
            <div class="hardware-name">Apple M4 芯片</div>
            <div class="description">3nm工艺，集成强大神经引擎的最新SoC，支持AI加速</div>
        </div>
        <div class="era3 software" data-category="software" data-industry="os ai">
            <div class="layer-indicator">软件</div>
            <div class="hardware-name">iOS 18 Apple Intelligence</div>
            <div class="description">本地AI功能集成，隐私保护的端侧智能</div>
        </div>
        <div class="era3 internet" data-category="internet" data-industry="cloud ai">
            <div class="layer-indicator">互联网服务</div>
            <div class="hardware-name">AWS Bedrock</div>
            <div class="description">云端AI模型服务，大模型API民主化</div>
        </div>
        <div class="era3 ai" data-category="ai" data-industry="nlp vision">
            <div class="layer-indicator">AI服务</div>
            <div class="hardware-name">GPT-4o</div>
            <div class="description">多模态AI模型，实时语音视觉交互突破</div>
        </div>
    </div>
</div>

<!-- 2023 -->
<div class="year-bar">
    <div class="year-header">
        <div class="year-label">2023</div>
        <div class="year-events">
            <div class="events-section">
                <div class="events-title success">重要推动事件</div>
                <div class="events-list">• ChatGPT用户突破1亿，创造互联网历史最快增长记录 • 生成式AI爆发，AIGC成为新风口 • 苹果发布Vision Pro，空间计算新时代 • AI芯片需求激增，算力成为新石油</div>
            </div>
            <div class="events-section">
                <div class="events-title" style="color:#4CAF50;">硬件事件</div>
                <div class="events-list">• 英伟达H100 GPU成为AI训练标配，供不应求 • 苹果M2 Ultra发布，76核GPU性能突破 • AMD RDNA 3架构发布，与英伟达竞争加剧 • 台积电3nm工艺量产，先进制程竞争白热化</div>
            </div>
            <div class="events-section">
                <div class="events-title" style="color:#2196F3;">软件事件</div>
                <div class="events-list">• ChatGPT引发AI应用开发热潮 • GitHub Copilot X发布，AI编程助手进化 • Adobe集成生成式AI，创意软件革命 • 微软Office 365集成Copilot，办公软件AI化</div>
            </div>
            <div class="events-section">
                <div class="events-title" style="color:#FF9800;">互联网服务</div>
                <div class="events-list">• OpenAI API服务爆发，开发者生态繁荣 • 百度文心一言、阿里通义千问等国产大模型发布 • 微软Bing集成ChatGPT，搜索引擎AI化 • TikTok面临全球监管压力，数据安全成焦点</div>
            </div>
            <div class="events-section">
                <div class="events-title" style="color:#9C27B0;">AI服务</div>
                <div class="events-list">• GPT-4发布，多模态能力大幅提升 • Midjourney V5图像生成质量接近真实照片 • Claude 2发布，长文本处理能力突出 • AutoGPT等AI Agent概念兴起，自主任务执行</div>
            </div>
            <div class="events-section">
                <div class="events-title failure">失败案例</div>
                <div class="events-list">• 硅谷银行倒闭，科技行业融资环境恶化 • Twitter重塑为X后用户流失，广告收入下降 • FTX破产震动加密货币行业，监管趋严 • 科技公司大规模裁员，AI替代人工担忧加剧</div>
            </div>
        </div>
    </div>
    <div class="step">
        <div class="era3 hardware active" data-category="hardware" data-industry="ai processor">
            <div class="layer-indicator">硬件</div>
            <div class="hardware-name">Nvidia H100 GPU</div>
            <div class="description">Hopper架构，80GB HBM3，AI训练和推理顶级性能</div>
        </div>
        <div class="era3 software" data-category="software" data-industry="ai application">
            <div class="layer-indicator">软件</div>
            <div class="hardware-name">ChatGPT</div>
            <div class="description">现象级AI应用，用户突破1亿，引发AI革命</div>
        </div>
        <div class="era3 internet" data-category="internet" data-industry="ai cloud">
            <div class="layer-indicator">互联网服务</div>
            <div class="hardware-name">OpenAI API</div>
            <div class="description">AI能力API化，开发者生态爆发式增长</div>
        </div>
        <div class="era3 ai" data-category="ai" data-industry="nlp vision">
            <div class="layer-indicator">AI服务</div>
            <div class="hardware-name">GPT-4</div>
            <div class="description">多模态大语言模型，文本图像理解能力突破</div>
        </div>
    </div>
</div>

<!-- 2022 -->
<div class="year-bar">
    <div class="year-header">
        <div class="year-label">2022</div>
        <div class="year-events">
            <div class="events-section">
                <div class="events-title success">重要事件</div>
                <div class="events-list">• OpenAI发布ChatGPT，引发全球AI热潮 • 苹果发布M2芯片，自研芯片性能持续领先 • 英伟达发布H100 AI芯片，专为大模型训练设计 • 中国空间站建设完成，太空计算能力大幅提升</div>
            </div>
            <div class="events-section">
                <div class="events-title failure">失败案例</div>
                <div class="events-list">• Meta元宇宙投资巨亏，股价暴跌超70% • 英特尔Arc显卡延期发布，性能不及预期 • 加密货币市场崩盘，Terra Luna、FTX等项目失败 • Netflix首次出现用户负增长，流媒体竞争加剧</div>
            </div>
        </div>
    </div>
    <div class="step">
        <div class="era3 hardware active" data-category="hardware" data-industry="processor">
            <div class="layer-indicator">硬件</div>
            <div class="hardware-name">Apple M2 芯片</div>
            <div class="description">5nm工艺，第二代自研SoC，性能进一步提升</div>
        </div>
        <div class="era3 software" data-category="software" data-industry="os">
            <div class="layer-indicator">软件</div>
            <div class="hardware-name">macOS Ventura</div>
            <div class="description">支持M2芯片优化，Stage Manager多任务管理</div>
        </div>
        <div class="era3 internet" data-category="internet" data-industry="cloud">
            <div class="layer-indicator">互联网服务</div>
            <div class="hardware-name">Microsoft 365 Copilot</div>
            <div class="description">AI助手集成Office套件，提升办公效率</div>
        </div>
        <div class="era3 ai" data-category="ai" data-industry="nlp">
            <div class="layer-indicator">AI服务</div>
            <div class="hardware-name">DALL-E 2</div>
            <div class="description">文本生成图像AI模型，创意内容生成</div>
        </div>
    </div>
</div>

<!-- 2021 -->
<div class="year-bar">
    <div class="year-header">
        <div class="year-label">2021</div>
        <div class="year-events">
            <div class="events-section">
                <div class="events-title success">重要事件</div>
                <div class="events-list">• 苹果发布M1 Pro/Max芯片，ARM架构在高性能计算领域取得突破 • 全球芯片短缺危机，凸显半导体产业重要性 • NFT和元宇宙概念爆火，数字资产市场兴起 • SpaceX成功发射平民太空任务，商业航天里程碑</div>
            </div>
            <div class="events-section">
                <div class="events-title failure">失败案例</div>
                <div class="events-list">• Facebook改名Meta遭遇质疑，元宇宙概念过度炒作 • 英特尔在移动芯片市场彻底败退，退出5G基带业务 • 多家自动驾驶公司推迟商业化时间表 • Windows 11发布初期兼容性问题频发</div>
            </div>
        </div>
    </div>
    <div class="step">
        <div class="era3 hardware active" data-category="hardware" data-industry="processor">
            <div class="layer-indicator">硬件</div>
            <div class="hardware-name">Apple M1 Max/Pro</div>
            <div class="description">5nm工艺，高性能SoC，用于MacBook Pro</div>
        </div>
        <div class="era3 software" data-category="software" data-industry="os">
            <div class="layer-indicator">软件</div>
            <div class="hardware-name">Windows 11</div>
            <div class="description">全新设计界面，支持Android应用，Teams集成</div>
        </div>
        <div class="era3 internet" data-category="internet" data-industry="social">
            <div class="layer-indicator">互联网服务</div>
            <div class="hardware-name">TikTok 全球化</div>
            <div class="description">短视频平台全球用户突破10亿，内容创作新模式</div>
        </div>
        <div class="era3 ai" data-category="ai" data-industry="vision">
            <div class="layer-indicator">AI服务</div>
            <div class="hardware-name">Tesla FSD Beta</div>
            <div class="description">全自动驾驶测试版，端到端神经网络驾驶</div>
        </div>
    </div>
</div>

<!-- 2020 -->
<div class="year-bar">
    <div class="year-header">
        <div class="year-label">2020</div>
        <div class="year-events">
            <div class="events-section">
                <div class="events-title success">重要事件</div>
                <div class="events-list">• 苹果发布M1芯片，ARM架构首次在PC市场取得成功 • 新冠疫情推动远程办公和数字化转型加速 • OpenAI发布GPT-3，展示大语言模型巨大潜力 • 5G网络开始大规模商用部署</div>
            </div>
            <div class="events-section">
                <div class="events-title failure">失败案例</div>
                <div class="events-list">• 英特尔7nm工艺再次延期，制程工艺落后台积电 • Quibi流媒体平台烧光17亿美元后倒闭 • WeWork IPO失败，估值从470亿美元暴跌至80亿 • 多家共享经济公司在疫情中倒闭</div>
            </div>
        </div>
    </div>
    <div class="step">
        <div class="era3 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Apple M1 芯片</div>
            <div class="description">5nm工艺，ARM架构进入PC市场，统一内存架构</div>
        </div>
        <div class="era3 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">5G 网络商用</div>
            <div class="description">高带宽、低延迟移动网络，峰值速度20Gbps</div>
        </div>
        <div class="era3 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Nvidia A100</div>
            <div class="description">Ampere架构，40GB HBM2，AI、数据分析专用GPU</div>
        </div>
    </div>
</div>

<!-- 2019 -->
<div class="year-bar">
    <div class="year-header">
        <div class="year-label">2019</div>
        <div class="year-events">
            <div class="events-section">
                <div class="events-title success">重要事件</div>
                <div class="events-list">• AMD发布7nm Ryzen 3000系列，重新获得CPU性能领先地位 • 谷歌宣布实现"量子霸权"，Sycamore处理器完成特定计算 • 5G网络开始商用，华为、爱立信等设备商竞争激烈 • Disney+流媒体服务上线，挑战Netflix地位</div>
            </div>
            <div class="events-section">
                <div class="events-title failure">失败案例</div>
                <div class="events-list">• 华为遭美国制裁，失去Google服务和芯片供应 • 波音737 MAX两次坠机，软件缺陷导致346人死亡 • Google Stadia云游戏服务表现不佳，延迟问题严重 • Uber和Lyft IPO后股价大跌，盈利模式受质疑</div>
            </div>
        </div>
    </div>
    <div class="step">
        <div class="era3 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">AMD Ryzen 3000</div>
            <div class="description">7nm工艺，Zen 2架构，与英特尔激烈竞争</div>
        </div>
        <div class="era3 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">Wi-Fi 6 (802.11ax)</div>
            <div class="description">新一代Wi-Fi标准，9.6Gbps理论速度</div>
        </div>
        <div class="era3 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Google Edge TPU</div>
            <div class="description">边缘设备AI推理专用芯片，4TOPS算力</div>
        </div>
    </div>
</div>

<!-- 2018 -->
<div class="year-bar">
    <div class="year-header">
        <div class="year-label">2018</div>
        <div class="year-events">
            <div class="events-section">
                <div class="events-title success">重要事件</div>
                <div class="events-list">• 苹果发布A12 Bionic，首个7nm手机芯片，集成神经引擎 • 英伟达发布RTX 20系列，首次实现实时光线追踪 • 中美贸易战升级，科技行业成为焦点 • GDPR生效，全球数据保护法规趋严</div>
            </div>
            <div class="events-section">
                <div class="events-title failure">失败案例</div>
                <div class="events-list">• Facebook剑桥分析丑闻，8700万用户数据泄露 • 英特尔CPU漏洞Spectre和Meltdown曝光，影响全球计算机 • 加密货币市场崩盘，比特币从2万美元跌至3200美元 • Google+社交网络关闭，用户数据泄露问题严重</div>
            </div>
        </div>
    </div>
    <div class="step">
        <div class="era3 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">iPhone XS (A12 Bionic)</div>
            <div class="description">7nm工艺，首个7nm手机芯片，神经引擎</div>
        </div>
        <div class="era3 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">USB 3.2</div>
            <div class="description">20Gbps传输速度，向下兼容</div>
        </div>
        <div class="era3 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Nvidia Turing (RTX 2080)</div>
            <div class="description">实时光线追踪，DLSS技术，12nm工艺</div>
        </div>
    </div>
</div>

<!-- 2017 -->
<div class="year-bar">
    <div class="year-label">2017</div>
    <div class="step">
        <div class="era3 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">iPhone X (A11 Bionic)</div>
            <div class="description">Face ID，神经引擎，推动移动AI应用</div>
        </div>
        <div class="era3 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">Thunderbolt 3</div>
            <div class="description">40Gbps带宽，USB-C接口，支持外接显卡</div>
        </div>
        <div class="era3 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Nvidia Tesla V100</div>
            <div class="description">Volta架构，Tensor Core，极大加速AI训练</div>
        </div>
    </div>
</div>

<!-- 2016 -->
<div class="year-bar">
    <div class="year-header">
        <div class="year-label">2016</div>
        <div class="year-events">
            <div class="events-section">
                <div class="events-title success">重要事件</div>
                <div class="events-list">• AlphaGo击败李世石，AI首次在复杂策略游戏中战胜人类顶尖选手 • 英伟达发布Pascal架构GPU，深度学习性能大幅提升 • 特斯拉Autopilot系统推出，自动驾驶技术商业化 • VR元年，Oculus Rift、HTC Vive等产品上市</div>
            </div>
            <div class="events-section">
                <div class="events-title failure">失败案例</div>
                <div class="events-list">• 三星Galaxy Note 7电池爆炸，全球召回损失超50亿美元 • 微软Kinect停产，体感游戏市场萎缩 • Yahoo被Verizon收购，互联网巨头没落 • Theranos血检技术造假丑闻曝光，估值从90亿美元归零</div>
            </div>
        </div>
    </div>
    <div class="step">
        <div class="era3 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">AlphaGo</div>
            <div class="description">击败世界围棋冠军，AI发展史上的里程碑</div>
        </div>
        <div class="era3 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">100G 光模块 (QSFP28)</div>
            <div class="description">数据中心网络主流方案，支持100G以太网</div>
        </div>
        <div class="era3 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Nvidia DGX-1</div>
            <div class="description">首台深度学习超级计算机，8个Tesla P100</div>
        </div>
    </div>
</div>

<!-- 2015 -->
<div class="year-bar">
    <div class="year-label">2015</div>
    <div class="step">
        <div class="era3 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Apple Watch</div>
            <div class="description">定义现代智能手表，可穿戴设备新时代</div>
        </div>
        <div class="era3 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">USB Type-C</div>
            <div class="description">统一接口标准，支持数据、视频、电源</div>
        </div>
        <div class="era3 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Google TPU v1</div>
            <div class="description">谷歌首款AI专用芯片，用于AlphaGo</div>
        </div>
    </div>
</div>

<!-- 2014 -->
<div class="year-bar">
    <div class="year-label">2014</div>
    <div class="step">
        <div class="era3 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Intel Broadwell</div>
            <div class="description">14nm工艺，英特尔首次进入14nm时代</div>
        </div>
        <div class="era3 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">802.11ac Wave 2</div>
            <div class="description">千兆Wi-Fi，MU-MIMO技术</div>
        </div>
        <div class="era3 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">AWS Lambda</div>
            <div class="description">无服务器计算，开创FaaS模式</div>
        </div>
    </div>
</div>

<!-- 2013 -->
<div class="year-bar">
    <div class="year-label">2013</div>
    <div class="step">
        <div class="era3 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">iPhone 5S (A7)</div>
            <div class="description">首款64位手机处理器，Touch ID指纹识别</div>
        </div>
        <div class="era3 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">802.11ac</div>
            <div class="description">5GHz频段，理论速度1.3Gbps</div>
        </div>
        <div class="era3 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">PlayStation 4</div>
            <div class="description">AMD APU，8GB GDDR5，游戏主机新时代</div>
        </div>
    </div>
</div>

<!-- 2012 -->
<div class="year-bar">
    <div class="year-label">2012</div>
    <div class="step">
        <div class="era3 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Raspberry Pi</div>
            <div class="description">35美元单板计算机，推动创客运动</div>
        </div>
        <div class="era3 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">Thunderbolt 2</div>
            <div class="description">20Gbps带宽，支持4K显示器</div>
        </div>
        <div class="era3 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">AlexNet</div>
            <div class="description">深度学习突破，ImageNet竞赛冠军</div>
        </div>
    </div>
</div>

<!-- 2011 -->
<div class="year-bar">
    <div class="year-label">2011</div>
    <div class="step">
        <div class="era3 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">iPad 2</div>
            <div class="description">A5双核处理器，更薄更轻的平板</div>
        </div>
        <div class="era3 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">USB 3.0 普及</div>
            <div class="description">5Gbps传输速度，是USB 2.0的十倍</div>
        </div>
        <div class="era3 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">IBM Watson</div>
            <div class="description">AI系统，在智力竞赛中击败人类冠军</div>
        </div>
    </div>
</div>

<!-- 2010 -->
<div class="year-bar">
    <div class="year-label">2010</div>
    <div class="step">
        <div class="era3 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">iPad</div>
            <div class="description">A4处理器，开创平板电脑市场</div>
        </div>
        <div class="era2 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">4G LTE</div>
            <div class="description">移动宽带网络，理论速度100Mbps</div>
        </div>
        <div class="era2 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">云计算普及</div>
            <div class="description">AWS、Azure等云服务成为主流</div>
        </div>
    </div>
</div>

<!-- 2009 -->
<div class="year-bar">
    <div class="year-label">2009</div>
    <div class="step">
        <div class="era2 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Intel Nehalem</div>
            <div class="description">45nm工艺，集成内存控制器，性能大幅提升</div>
        </div>
        <div class="era2 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">USB 3.0 标准</div>
            <div class="description">SuperSpeed USB，5Gbps传输速度</div>
        </div>
        <div class="era2 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">SSD 普及</div>
            <div class="description">固态硬盘开始普及，存储性能革命</div>
        </div>
    </div>
</div>

<!-- 2008 -->
<div class="year-bar">
    <div class="year-label">2008</div>
    <div class="step">
        <div class="era2 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">App Store</div>
            <div class="description">移动应用生态系统，改变软件分发模式</div>
        </div>
        <div class="era2 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">DisplayPort 1.0</div>
            <div class="description">新一代显示接口标准，支持高分辨率</div>
        </div>
        <div class="era2 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Intel Atom</div>
            <div class="description">低功耗处理器，专为上网本设计</div>
        </div>
    </div>
</div>

<!-- 2007 -->
<div class="year-bar">
    <div class="year-header">
        <div class="year-label">2007</div>
        <div class="year-events">
            <div class="events-section">
                <div class="events-title success">重要事件</div>
                <div class="events-list">• 苹果发布iPhone，重新定义智能手机，开启移动互联网时代 • Amazon推出EC2云计算服务，云计算商业化开端 • Google发布Android系统，挑战iOS垄断 • Netflix开始流媒体服务，颠覆传统娱乐产业</div>
            </div>
            <div class="events-section">
                <div class="events-title failure">失败案例</div>
                <div class="events-list">• 微软Vista操作系统发布失败，兼容性和性能问题严重 • HD DVD败给蓝光，东芝损失数十亿美元 • Second Life虚拟世界热潮退去，用户大量流失 • 次贷危机爆发，科技股大幅下跌</div>
            </div>
        </div>
    </div>
    <div class="step">
        <div class="era2 hardware active" data-category="hardware" data-industry="mobile">
            <div class="layer-indicator">硬件</div>
            <div class="hardware-name">iPhone</div>
            <div class="description">重新定义智能手机，多点触控革命</div>
        </div>
        <div class="era2 software" data-category="software" data-industry="os">
            <div class="layer-indicator">软件</div>
            <div class="hardware-name">iOS (iPhone OS)</div>
            <div class="description">移动操作系统，触控界面设计典范</div>
        </div>
        <div class="era2 internet" data-category="internet" data-industry="cloud">
            <div class="layer-indicator">互联网服务</div>
            <div class="hardware-name">Amazon EC2</div>
            <div class="description">弹性云计算服务，IaaS模式开创</div>
        </div>
        <div class="era2 ai" data-category="ai" data-industry="recommendation">
            <div class="layer-indicator">AI服务</div>
            <div class="hardware-name">Netflix 推荐算法</div>
            <div class="description">协同过滤推荐系统，个性化内容推荐</div>
        </div>
    </div>
</div>

<!-- 2006 -->
<div class="year-bar">
    <div class="year-label">2006</div>
    <div class="step">
        <div class="era2 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Intel Core 2 Duo</div>
            <div class="description">65nm工艺，双核处理器，性能功耗平衡</div>
        </div>
        <div class="era2 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">SATA 3.0</div>
            <div class="description">6Gbps传输速度，支持高速SSD</div>
        </div>
        <div class="era2 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Amazon S3</div>
            <div class="description">云存储服务，按需付费存储模式</div>
        </div>
    </div>
</div>

<!-- 2005 -->
<div class="year-bar">
    <div class="year-label">2005</div>
    <div class="step">
        <div class="era2 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">YouTube</div>
            <div class="description">在线视频分享平台，改变内容消费方式</div>
        </div>
        <div class="era2 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">PCI Express 2.0</div>
            <div class="description">5GT/s传输速度，显卡接口标准</div>
        </div>
        <div class="era2 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Google MapReduce</div>
            <div class="description">大数据处理模型，分布式计算框架</div>
        </div>
    </div>
</div>

<!-- 2004 -->
<div class="year-bar">
    <div class="year-label">2004</div>
    <div class="step">
        <div class="era2 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Facebook</div>
            <div class="description">社交网络平台，改变人际交流方式</div>
        </div>
        <div class="era2 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">PCI Express 1.0</div>
            <div class="description">2.5GT/s传输速度，取代AGP和PCI</div>
        </div>
        <div class="era2 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">AMD64 架构</div>
            <div class="description">64位x86扩展，被英特尔采纳</div>
        </div>
    </div>
</div>

<!-- 2003 -->
<div class="year-bar">
    <div class="year-label">2003</div>
    <div class="step">
        <div class="era2 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">iTunes Store</div>
            <div class="description">数字音乐商店，改变音乐产业</div>
        </div>
        <div class="era2 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">Wi-Fi 802.11g</div>
            <div class="description">54Mbps无线网络，2.4GHz频段</div>
        </div>
        <div class="era2 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">AMD Opteron</div>
            <div class="description">首款x86-64服务器处理器</div>
        </div>
    </div>
</div>

<!-- 2002 -->
<div class="year-bar">
    <div class="year-label">2002</div>
    <div class="step">
        <div class="era2 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Tablet PC</div>
            <div class="description">微软早期平板电脑尝试，触控笔输入</div>
        </div>
        <div class="era2 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">SATA 1.0</div>
            <div class="description">1.5Gbps串行ATA，取代PATA</div>
        </div>
        <div class="era2 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">刀片服务器</div>
            <div class="description">高密度服务器，提升数据中心效率</div>
        </div>
    </div>
</div>

<!-- 2001 -->
<div class="year-bar">
    <div class="year-label">2001</div>
    <div class="step">
        <div class="era2 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">iPod</div>
            <div class="description">5GB硬盘，"1000首歌装进口袋"</div>
        </div>
        <div class="era2 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">Wi-Fi 802.11b</div>
            <div class="description">11Mbps无线局域网普及</div>
        </div>
        <div class="era2 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Intel Itanium</div>
            <div class="description">64位EPIC架构，高端服务器处理器</div>
        </div>
    </div>
</div>

<!-- 2000 -->
<div class="year-bar">
    <div class="year-label">2000</div>
    <div class="step">
        <div class="era2 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">PlayStation 2</div>
            <div class="description">DVD播放，向下兼容，史上最畅销游戏主机</div>
        </div>
        <div class="era2 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">USB 2.0</div>
            <div class="description">480Mbps高速USB，成为主流接口</div>
        </div>
        <div class="era2 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Intel Pentium 4</div>
            <div class="description">NetBurst架构，高频率设计</div>
        </div>
    </div>
</div>

<!-- 1999 -->
<div class="year-bar">
    <div class="year-label">1999</div>
    <div class="step">
        <div class="era2 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Nvidia GeForce 256</div>
            <div class="description">首款GPU，图形处理单元概念确立</div>
        </div>
        <div class="era2 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">Wi-Fi 802.11a</div>
            <div class="description">54Mbps，5GHz频段无线网络</div>
        </div>
        <div class="era2 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">AMD Athlon</div>
            <div class="description">首个突破1GHz的消费级处理器</div>
        </div>
    </div>
</div>

<!-- 1998 -->
<div class="year-bar">
    <div class="year-label">1998</div>
    <div class="step">
        <div class="era2 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Google 搜索引擎</div>
            <div class="description">PageRank算法，改变信息检索方式</div>
        </div>
        <div class="era2 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">IEEE 1394 (FireWire)</div>
            <div class="description">400Mbps高速串行总线</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Intel Celeron</div>
            <div class="description">低成本处理器，普及PC市场</div>
        </div>
    </div>
</div>

<!-- 1997 -->
<div class="year-bar">
    <div class="year-label">1997</div>
    <div class="step">
        <div class="era2 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Intel Pentium II</div>
            <div class="description">MMX技术，增强多媒体处理能力</div>
        </div>
        <div class="era2 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">AGP 1.0</div>
            <div class="description">专用显卡接口，266MB/s带宽</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">DVD 标准</div>
            <div class="description">数字视频光盘，4.7GB容量</div>
        </div>
    </div>
</div>

<!-- 1996 -->
<div class="year-bar">
    <div class="year-label">1996</div>
    <div class="step">
        <div class="era2 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">3dfx Voodoo</div>
            <div class="description">3D图形加速卡，游戏图形革命</div>
        </div>
        <div class="era2 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">USB 1.0</div>
            <div class="description">通用串行总线，12Mbps传输速度</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Intel Pentium Pro</div>
            <div class="description">32位服务器和工作站处理器</div>
        </div>
    </div>
</div>

<!-- 1995 -->
<div class="year-bar">
    <div class="year-header">
        <div class="year-label">1995</div>
        <div class="year-events">
            <div class="events-section">
                <div class="events-title success">重要事件</div>
                <div class="events-list">• 微软发布Windows 95，图形界面操作系统普及，开启PC时代 • Netscape IPO成功，互联网商业化里程碑 • Amazon和eBay成立，电子商务兴起 • JavaScript语言发布，网页交互性大幅提升</div>
            </div>
            <div class="events-section">
                <div class="events-title failure">失败案例</div>
                <div class="events-list">• IBM OS/2操作系统败给Windows，错失PC操作系统市场 • Apple Newton PDA失败，手写识别技术不成熟 • 3DO游戏机因价格过高失败，被PlayStation击败 • 多家早期互联网公司在泡沫破裂前倒闭</div>
            </div>
        </div>
    </div>
    <div class="step">
        <div class="era2 hardware active" data-category="hardware" data-industry="networking">
            <div class="layer-indicator">硬件</div>
            <div class="hardware-name">56K 调制解调器</div>
            <div class="description">拨号上网最高速度，家庭上网标志</div>
        </div>
        <div class="era2 software" data-category="software" data-industry="os">
            <div class="layer-indicator">软件</div>
            <div class="hardware-name">Windows 95</div>
            <div class="description">32位操作系统，开始菜单和任务栏</div>
        </div>
        <div class="era2 internet" data-category="internet" data-industry="search">
            <div class="layer-indicator">互联网服务</div>
            <div class="hardware-name">Netscape Navigator</div>
            <div class="description">图形化网页浏览器，推动WWW普及</div>
        </div>
        <div class="era1 ai" data-category="ai" data-industry="expert">
            <div class="layer-indicator">AI服务</div>
            <div class="hardware-name">专家系统商用化</div>
            <div class="description">基于规则的AI系统在医疗、金融等领域应用</div>
        </div>
    </div>
</div>

<!-- 1994 -->
<div class="year-bar">
    <div class="year-label">1994</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Sony PlayStation</div>
            <div class="description">32位游戏主机，3D游戏时代开启</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">Fast Ethernet</div>
            <div class="description">100Mbps以太网标准</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Netscape 成立</div>
            <div class="description">网络浏览器公司，推动互联网商业化</div>
        </div>
    </div>
</div>

<!-- 1993 -->
<div class="year-bar">
    <div class="year-label">1993</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Intel Pentium</div>
            <div class="description">第五代x86处理器，超标量架构</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">Mosaic 浏览器</div>
            <div class="description">首款图形化网页浏览器</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Windows NT</div>
            <div class="description">32位企业级操作系统</div>
        </div>
    </div>
</div>

<!-- 1991 -->
<div class="year-bar">
    <div class="year-label">1991</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">World Wide Web</div>
            <div class="description">Tim Berners-Lee发明，改变信息共享</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">10BASE-T 以太网</div>
            <div class="description">双绞线以太网，10Mbps</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Linux 内核</div>
            <div class="description">Linus Torvalds开发的开源操作系统</div>
        </div>
    </div>
</div>

<!-- 1989 -->
<div class="year-bar">
    <div class="year-label">1989</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Intel 80486</div>
            <div class="description">内置数学协处理器，25-100MHz</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">ISDN</div>
            <div class="description">综合服务数字网，128Kbps</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">NeXTcube</div>
            <div class="description">乔布斯的高端工作站</div>
        </div>
    </div>
</div>

<!-- 1988 -->
<div class="year-bar">
    <div class="year-label">1988</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">VGA 显示标准</div>
            <div class="description">640×480分辨率，256色显示</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">T1 线路</div>
            <div class="description">1.544Mbps专线，企业级连接</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">RAID 技术</div>
            <div class="description">磁盘阵列，提高存储可靠性</div>
        </div>
    </div>
</div>

<!-- 1985 -->
<div class="year-bar">
    <div class="year-label">1985</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Intel 80386</div>
            <div class="description">首款32位x86处理器，虚拟内存</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">CD-ROM</div>
            <div class="description">光盘存储，650MB容量</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Commodore Amiga</div>
            <div class="description">先进多媒体功能，图形工作站</div>
        </div>
    </div>
</div>

<!-- 1984 -->
<div class="year-bar">
    <div class="year-label">1984</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Apple Macintosh</div>
            <div class="description">首款成功的GUI个人电脑，鼠标操作</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">3.5英寸软盘</div>
            <div class="description">1.44MB容量，更可靠的存储</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">DNS 系统</div>
            <div class="description">域名系统，简化网络地址</div>
        </div>
    </div>
</div>

<!-- 1983 -->
<div class="year-bar">
    <div class="year-label">1983</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Apple Lisa</div>
            <div class="description">首批GUI商用电脑，价格昂贵</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">TCP/IP 协议</div>
            <div class="description">ARPANET标准协议，互联网基石</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">IBM PC XT</div>
            <div class="description">首次标配硬盘的PC</div>
        </div>
    </div>
</div>

<!-- 1982 -->
<div class="year-bar">
    <div class="year-label">1982</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Commodore 64</div>
            <div class="description">史上最畅销的单一计算机型号</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">SMTP 协议</div>
            <div class="description">简单邮件传输协议</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Intel 80286</div>
            <div class="description">16位处理器，保护模式</div>
        </div>
    </div>
</div>

<!-- 1981 -->
<div class="year-bar">
    <div class="year-header">
        <div class="year-label">1981</div>
        <div class="year-events">
            <div class="events-section">
                <div class="events-title success">重要事件</div>
                <div class="events-list">• IBM发布PC 5150，确立个人电脑标准，开放架构推动产业发展 • 微软MS-DOS成为PC操作系统标准 • MTV电视台开播，"Video Killed the Radio Star" • 第一台便携式计算机Osborne 1发布</div>
            </div>
            <div class="events-section">
                <div class="events-title failure">失败案例</div>
                <div class="events-list">• Texas Instruments TI-99/4A家用电脑失败，价格策略错误 • Sinclair ZX81虽便宜但性能太弱，市场反应平淡 • 多家早期PC制造商因标准化竞争失败 • Xerox Star工作站因价格过高（7.5万美元）商业失败</div>
            </div>
        </div>
    </div>
    <div class="step">
        <div class="era1 hardware active" data-category="hardware" data-industry="processor">
            <div class="layer-indicator">硬件</div>
            <div class="hardware-name">IBM PC 5150</div>
            <div class="description">个人电脑标准确立，开放架构</div>
        </div>
        <div class="era1 software" data-category="software" data-industry="os">
            <div class="layer-indicator">软件</div>
            <div class="hardware-name">MS-DOS 1.0</div>
            <div class="description">微软磁盘操作系统</div>
        </div>
        <div class="era1 internet" data-category="internet" data-industry="communication">
            <div class="layer-indicator">互联网服务</div>
            <div class="hardware-name">CSNET 计划</div>
            <div class="description">计算机科学网络，连接大学和研究机构</div>
        </div>
        <div class="era1 ai" data-category="ai" data-industry="expert">
            <div class="layer-indicator">AI服务</div>
            <div class="hardware-name">第五代计算机项目</div>
            <div class="description">日本启动AI和并行处理研究计划</div>
        </div>
    </div>
</div>

<!-- 1978 -->
<div class="year-bar">
    <div class="year-label">1978</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">5.25英寸软盘</div>
            <div class="description">个人电脑软件和数据交换介质</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">调制解调器</div>
            <div class="description">300波特率，电话线数据传输</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Intel 8086</div>
            <div class="description">16位微处理器，x86架构起点</div>
        </div>
    </div>
</div>

<!-- 1977 -->
<div class="year-bar">
    <div class="year-label">1977</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Apple II</div>
            <div class="description">首款大规模成功的个人电脑</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">IEEE 488 总线</div>
            <div class="description">通用接口总线，仪器连接</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">DEC VAX-11/780</div>
            <div class="description">32位小型机，虚拟地址扩展</div>
        </div>
    </div>
</div>

<!-- 1976 -->
<div class="year-bar">
    <div class="year-label">1976</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Apple I</div>
            <div class="description">苹果公司首款产品，计算机爱好者</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">X.25 协议</div>
            <div class="description">分组交换网络协议</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Cray-1</div>
            <div class="description">首台商业超级计算机</div>
        </div>
    </div>
</div>

<!-- 1973 -->
<div class="year-bar">
    <div class="year-label">1973</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Xerox Alto</div>
            <div class="description">首台GUI工作站，鼠标和窗口</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">以太网发明</div>
            <div class="description">Xerox PARC发明，局域网标准</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Intel 8080</div>
            <div class="description">8位微处理器，个人电脑基础</div>
        </div>
    </div>
</div>

<!-- 1971 -->
<div class="year-bar">
    <div class="year-header">
        <div class="year-label">1971</div>
        <div class="year-events">
            <div class="events-section">
                <div class="events-title success">重要事件</div>
                <div class="events-list">• Intel发布4004微处理器，开启微处理器时代 • IBM发明8英寸软盘，便携式存储革命 • 第一封电子邮件通过ARPANET发送 • 德州仪器发明单芯片微控制器</div>
            </div>
            <div class="events-section">
                <div class="events-title failure">失败案例</div>
                <div class="events-list">• 多家大型机制造商忽视微处理器潜力 • 早期微处理器性能有限，应用场景受限 • 软盘容量小且易损坏，可靠性问题 • 网络连接成本极高，普及困难</div>
            </div>
        </div>
    </div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">8英寸软盘</div>
            <div class="description">IBM发明，首个便携式存储介质</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">ARPANET 首次连接</div>
            <div class="description">UCLA和斯坦福，互联网诞生</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Intel 4004</div>
            <div class="description">首款商用微处理器，4位</div>
        </div>
    </div>
</div>

<!-- 1969 -->
<div class="year-bar">
    <div class="year-label">1969</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">哑终端</div>
            <div class="description">连接大型机的字符终端</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">ARPANET IMP</div>
            <div class="description">接口消息处理器，网络节点</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Unix 操作系统</div>
            <div class="description">AT&T贝尔实验室开发</div>
        </div>
    </div>
</div>

<!-- 1975 -->
<div class="year-bar">
    <div class="year-label">1975</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Altair 8800</div>
            <div class="description">首台个人电脑套件，激发PC革命</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">S-100 总线</div>
            <div class="description">早期PC扩展总线标准</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Microsoft 成立</div>
            <div class="description">比尔·盖茨和保罗·艾伦创立</div>
        </div>
    </div>
</div>

<!-- 1974 -->
<div class="year-bar">
    <div class="year-label">1974</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Intel 8080</div>
            <div class="description">8位微处理器，个人电脑基础</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">Ethernet 原型</div>
            <div class="description">Xerox PARC，2.94Mbps原始版本</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">CP/M 操作系统</div>
            <div class="description">Gary Kildall开发，早期PC操作系统</div>
        </div>
    </div>
</div>

<!-- 1972 -->
<div class="year-bar">
    <div class="year-label">1972</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Intel 8008</div>
            <div class="description">8位微处理器，计算机小型化关键</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">电子邮件</div>
            <div class="description">Ray Tomlinson发明@符号邮件系统</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">C 语言</div>
            <div class="description">Dennis Ritchie开发，系统编程语言</div>
        </div>
    </div>
</div>

<!-- 1970 -->
<div class="year-bar">
    <div class="year-label">1970</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">PDP-11</div>
            <div class="description">DEC小型机，16位架构，影响深远</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">ALOHAnet</div>
            <div class="description">夏威夷大学，无线分组网络先驱</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">关系数据库理论</div>
            <div class="description">Edgar Codd提出，数据管理革命</div>
        </div>
    </div>
</div>

<!-- 1968 -->
<div class="year-bar">
    <div class="year-label">1968</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">鼠标演示</div>
            <div class="description">Douglas Engelbart，"所有演示之母"</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">分组交换理论</div>
            <div class="description">网络通信基础理论确立</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Intel 成立</div>
            <div class="description">Robert Noyce和Gordon Moore创立</div>
        </div>
    </div>
</div>

<!-- 1965 -->
<div class="year-bar">
    <div class="year-label">1965</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">PDP-8</div>
            <div class="description">DEC首台商业小型机，12位字长</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">首次广域网连接</div>
            <div class="description">MIT Lincoln Lab与System Development Corp</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">摩尔定律</div>
            <div class="description">Gordon Moore预测，芯片性能指数增长</div>
        </div>
    </div>
</div>

<!-- 1964 -->
<div class="year-bar">
    <div class="year-label">1964</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">BASIC 语言</div>
            <div class="description">达特茅斯学院，易学编程语言，推动计算机普及</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">9-track 磁带</div>
            <div class="description">IBM标准，数据存储和交换格式</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">IBM System/360</div>
            <div class="description">兼容性计算机系列，统一架构，现代计算机设计典范</div>
        </div>
    </div>
</div>

<!-- 1962 -->
<div class="year-bar">
    <div class="year-label">1962</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Spacewar!</div>
            <div class="description">首个计算机游戏，MIT在PDP-1上开发</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">Telstar 卫星</div>
            <div class="description">首颗商业通信卫星，全球通信开端</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">分时系统</div>
            <div class="description">MIT CTSS，多用户共享计算资源</div>
        </div>
    </div>
</div>

<!-- 1960 -->
<div class="year-bar">
    <div class="year-label">1960</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">PDP-1</div>
            <div class="description">DEC首台计算机，交互式计算先驱</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">COBOL 语言</div>
            <div class="description">商业导向编程语言，数据处理标准</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">IBM 1401</div>
            <div class="description">晶体管计算机，商业数据处理主力</div>
        </div>
    </div>
</div>

<!-- 1958 -->
<div class="year-bar">
    <div class="year-label">1958</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">集成电路发明</div>
            <div class="description">Jack Kilby和Robert Noyce，微电子革命开端</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">调制解调器原型</div>
            <div class="description">Bell 103，电话线数据传输</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">LISP 语言</div>
            <div class="description">John McCarthy开发，人工智能编程语言</div>
        </div>
    </div>
</div>

<!-- 1957 -->
<div class="year-bar">
    <div class="year-label">1957</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">FORTRAN 语言</div>
            <div class="description">IBM开发，首个高级编程语言</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">Sputnik 卫星</div>
            <div class="description">苏联发射，开启太空通信时代</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">IBM 704</div>
            <div class="description">浮点运算，科学计算主力机</div>
        </div>
    </div>
</div>

<!-- 1951 -->
<div class="year-bar">
    <div class="year-label">1951</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">UNIVAC I</div>
            <div class="description">首台商业计算机，预测艾森豪威尔当选</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">磁带存储</div>
            <div class="description">UNIVAC使用，大容量数据存储</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Whirlwind I</div>
            <div class="description">MIT实时计算机，磁芯存储器</div>
        </div>
    </div>
</div>

<!-- 1948 -->
<div class="year-bar">
    <div class="year-label">1948</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">晶体管发明</div>
            <div class="description">贝尔实验室，固态电子器件革命</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">信息论</div>
            <div class="description">Claude Shannon，数字通信理论基础</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Manchester Baby</div>
            <div class="description">首台存储程序计算机</div>
        </div>
    </div>
</div>

<!-- 1946 -->
<div class="year-bar">
    <div class="year-header">
        <div class="year-label">1946</div>
        <div class="year-events">
            <div class="events-section">
                <div class="events-title success">重要事件</div>
                <div class="events-list">• ENIAC在宾夕法尼亚大学首次公开展示，现代计算机时代开始 • 冯·诺依曼发表存储程序概念，奠定现代计算机架构基础 • 第一台通用电子数字计算机投入使用 • 计算速度比人工计算快1000倍，震惊世界</div>
            </div>
            <div class="events-section">
                <div class="events-title failure">失败案例</div>
                <div class="events-list">• ENIAC耗电量巨大（150千瓦），运行成本极高 • 电子管频繁烧毁，可靠性差，平均每天故障数次 • 编程需要重新布线，操作极其复杂 • 体积庞大（30吨），无法移动，应用场景受限</div>
            </div>
        </div>
    </div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">ENIAC</div>
            <div class="description">首台通用电子计算机，30吨重，18000个电子管</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">冯·诺依曼架构</div>
            <div class="description">存储程序概念，现代计算机基础</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">电子管计算</div>
            <div class="description">真空管技术，第一代计算机核心</div>
        </div>
    </div>
</div>
</div>

<script>
let currentCategory = 'hardware';
let currentIndustry = 'all';

function toggleSubMenu(category, button) {
    // 隐藏所有子菜单
    document.querySelectorAll('.sub-menu').forEach(menu => {
        menu.style.display = 'none';
    });

    // 重置所有主按钮文本
    document.querySelectorAll('.tab-button').forEach((btn, index) => {
        btn.classList.remove('active');
        const categories = ['① 硬件', '② 软件', '③ 互联网服务', '④ AI服务'];
        btn.textContent = categories[index];
    });

    // 激活当前按钮
    button.classList.add('active');

    // 显示对应的子菜单
    const submenu = document.getElementById(category + '-submenu');
    if (submenu) {
        submenu.style.display = 'block';
    }

    // 更新当前分类
    currentCategory = category;
    currentIndustry = 'all';

    // 重置子菜单按钮状态
    document.querySelectorAll('.sub-button').forEach(btn => {
        btn.classList.remove('active');
    });
    submenu.querySelector('.sub-button').classList.add('active');

    // 应用过滤
    filterContent();
}

function filterByIndustry(category, industry, button) {
    // 移除同级按钮的active类
    button.parentElement.querySelectorAll('.sub-button').forEach(btn => {
        btn.classList.remove('active');
    });

    // 激活当前按钮
    button.classList.add('active');

    // 更新当前行业
    currentIndustry = industry;

    // 更新主按钮文本显示当前选中的行业
    const mainButton = button.closest('.tab-group').querySelector('.tab-button');
    const categoryNames = {
        'hardware': '① 硬件',
        'software': '② 软件',
        'internet': '③ 互联网服务',
        'ai': '④ AI服务'
    };
    const industryText = industry === 'all' ? '' : ` - ${button.textContent}`;
    mainButton.textContent = categoryNames[category] + industryText;

    // 应用过滤
    filterContent();

    // 隐藏子菜单
    setTimeout(() => {
        button.parentElement.style.display = 'none';
    }, 200); // 延迟200ms隐藏，让用户看到选中效果
}

function filterContent() {
    const allItems = document.querySelectorAll('.step > div');

    allItems.forEach(item => {
        const itemCategory = item.dataset.category;
        const itemIndustry = item.dataset.industry;

        // 检查分类匹配
        const categoryMatch = itemCategory === currentCategory;

        // 检查行业匹配
        let industryMatch = true;
        if (currentIndustry !== 'all') {
            industryMatch = itemIndustry && itemIndustry.includes(currentIndustry);
        }

        // 显示或隐藏项目
        if (categoryMatch && industryMatch) {
            item.classList.add('active');
        } else {
            item.classList.remove('active');
        }
    });
}

// 行业分类映射
const industryMapping = {
    // 终端设备行业映射
    'Apple M4 芯片': 'consumer computing',
    'Apple Vision Pro': 'consumer wearable',
    'Apple M2 芯片': 'consumer computing',
    'Apple M1 Max/Pro': 'consumer computing',
    'Apple M1 芯片': 'consumer computing',
    'AMD Ryzen 3000': 'consumer computing',
    'iPhone XS (A12 Bionic)': 'consumer mobile',
    'iPhone X (A11 Bionic)': 'consumer mobile',
    'AlphaGo': 'ai computing',
    'Apple Watch': 'consumer wearable',
    'Intel Broadwell': 'consumer computing',
    'iPhone 5S (A7)': 'consumer mobile',
    'Raspberry Pi': 'iot computing',
    'iPad 2': 'consumer mobile',
    'iPad': 'consumer mobile',
    'Intel Nehalem': 'consumer computing',
    'App Store': 'mobile consumer',
    'iPhone': 'consumer mobile',
    'Intel Core 2 Duo': 'consumer computing',
    'YouTube': 'consumer',
    'Facebook': 'consumer',
    'iTunes Store': 'consumer',
    'Tablet PC': 'consumer computing',
    'iPod': 'consumer',
    'Windows 95': 'consumer computing os',
    'Nvidia GeForce 256': 'consumer gaming',
    'Google 搜索引擎': 'consumer',
    'Intel Pentium II': 'consumer computing',
    '3dfx Voodoo': 'consumer gaming',
    'PlayStation 2': 'consumer gaming',
    'Intel Pentium 4': 'consumer computing',
    'Sony PlayStation': 'consumer gaming',
    'Intel Pentium': 'consumer computing',
    'World Wide Web': 'consumer',
    'Intel 80486': 'consumer computing',
    'VGA 显示标准': 'consumer computing',
    'Intel 80386': 'consumer computing',
    'Apple Macintosh': 'consumer computing',
    'Apple Lisa': 'consumer computing',
    'Commodore 64': 'consumer computing',
    'IBM PC 5150': 'consumer computing',
    '5.25英寸软盘': 'consumer computing',
    'Apple II': 'consumer computing',
    'Apple I': 'consumer computing',
    'Xerox Alto': 'consumer computing',
    '8英寸软盘': 'computing storage',
    '哑终端': 'enterprise computing',
    'BASIC 语言': 'computing',
    'ENIAC': 'enterprise computing',

    // 传输设备行业映射
    'Wi-Fi 7 (802.11be)': 'wireless networking',
    '800G 光模块': 'optical datacenter',
    'CXL 2.0': 'datacenter',
    'DDR5 SDRAM': 'computing storage',
    '5G 网络商用': 'wireless telecom',
    'Wi-Fi 6 (802.11ax)': 'wireless networking',
    'USB 3.2': 'computing storage',
    'Thunderbolt 3': 'computing storage',
    '100G 光模块 (QSFP28)': 'optical datacenter',
    'USB Type-C': 'computing storage',
    '802.11ac Wave 2': 'wireless networking',
    '802.11ac': 'wireless networking',
    'Thunderbolt 2': 'computing storage',
    'USB 3.0 普及': 'computing storage',
    '4G LTE': 'wireless telecom',
    'USB 3.0 标准': 'computing storage',
    'DisplayPort 1.0': 'computing',
    '3G 网络 (HSPA)': 'wireless telecom',
    'SATA 3.0': 'computing storage',
    'PCI Express 2.0': 'computing',
    'Wi-Fi 802.11g': 'wireless networking',
    'SATA 1.0': 'computing storage',
    'Wi-Fi 802.11b': 'wireless networking',
    'USB 2.0': 'computing storage',
    'Wi-Fi 802.11a': 'wireless networking',
    'IEEE 1394 (FireWire)': 'computing storage',
    'AGP 1.0': 'computing',
    'USB 1.0': 'computing storage',
    '56K 调制解调器': 'telecom networking',
    'Fast Ethernet': 'networking',
    'Mosaic 浏览器': 'networking',
    '10BASE-T 以太网': 'networking',
    'ISDN': 'telecom',
    'T1 线路': 'telecom',
    '3.5英寸软盘': 'computing storage',
    'TCP/IP 协议': 'networking',
    'SMTP 协议': 'networking',
    'RS-232 串口': 'computing',
    'IEEE 488 总线': 'computing',
    'X.25 协议': 'networking telecom',
    '以太网发明': 'networking',
    'ARPANET 首次连接': 'networking',
    'ARPANET IMP': 'networking',
    '9-track 磁带': 'computing storage',
    'Telstar 卫星': 'satellite telecom',
    '首次广域网连接': 'networking',
    '分组交换理论': 'networking',
    'ALOHAnet': 'wireless networking',
    '电子邮件': 'networking',
    'Ethernet 原型': 'networking',
    'S-100 总线': 'computing',
    '主板总线': 'computing',
    '调制解调器': 'telecom networking',
    '调制解调器原型': 'telecom',
    'Sputnik 卫星': 'satellite',
    '磁带存储': 'computing storage',
    '信息论': 'computing',
    '冯·诺依曼架构': 'computing',

    // 服务设备行业映射
    'Nvidia H200': 'ai hpc',
    'ChatGPT/GPT-4': 'ai cloud',
    'Nvidia H100': 'ai hpc',
    'Google TPU v4': 'ai hpc',
    'Nvidia A100': 'ai hpc',
    'Google Edge TPU': 'ai iot',
    'Nvidia Turing (RTX 2080)': 'consumer gaming hpc',
    'Nvidia Tesla V100': 'ai hpc',
    'Nvidia DGX-1': 'ai hpc enterprise',
    'Google TPU v1': 'ai hpc',
    'AWS Lambda': 'cloud enterprise',
    'PlayStation 4': 'consumer gaming',
    'AlexNet': 'ai',
    'IBM Watson': 'ai enterprise',
    '云计算普及': 'cloud enterprise',
    'Amazon EC2': 'cloud enterprise',
    'Amazon S3': 'cloud storage',
    'Google MapReduce': 'enterprise database',
    'AMD64 架构': 'computing enterprise',
    'AMD Opteron': 'enterprise hpc',
    '刀片服务器': 'enterprise datacenter',
    'Intel Itanium': 'enterprise hpc',
    '机架式服务器': 'enterprise datacenter',
    'Intel Celeron': 'consumer computing',
    'Intel Pentium Pro': 'enterprise computing',
    'DVD 标准': 'consumer storage',
    'Netscape Navigator': 'consumer os',
    'Netscape 成立': 'enterprise',
    'Windows NT': 'enterprise os',
    'Linux 内核': 'os',
    'NeXTcube': 'enterprise computing',
    'RAID 技术': 'enterprise storage',
    'Commodore Amiga': 'consumer computing',
    'DNS 系统': 'networking enterprise',
    'IBM PC XT': 'consumer computing',
    'Intel 80286': 'computing',
    'MS-DOS 1.0': 'os',
    'Intel 8086': 'computing',
    'DEC VAX-11/780': 'enterprise hpc',
    'Cray-1': 'hpc enterprise',
    'Intel 8080': 'computing',
    'CP/M 操作系统': 'os',
    'C 语言': 'computing',
    '关系数据库理论': 'database enterprise',
    'Intel 成立': 'enterprise',
    '摩尔定律': 'computing',
    'IBM System/360': 'enterprise hpc',
    '分时系统': 'enterprise computing',
    'COBOL 语言': 'enterprise computing',
    'IBM 1401': 'enterprise computing',
    'LISP 语言': 'ai computing',
    'IBM 704': 'enterprise hpc',
    'FORTRAN 语言': 'hpc computing',
    'Whirlwind I': 'enterprise computing',
    'Manchester Baby': 'computing',
    '电子管计算': 'computing'
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('计算机发展时间轴天梯图加载完成');

    // 批量修复旧的分类名称
    document.querySelectorAll('.step > div').forEach((item, index) => {
        const stepIndex = index % 4; // 每个step有4个div

        // 根据位置分配新的分类
        if (stepIndex === 0) {
            // 第一个位置：硬件
            item.setAttribute('data-category', 'hardware');
            item.className = item.className.replace(/terminal|transmission|service/, 'hardware');
            const indicator = item.querySelector('.layer-indicator');
            if (indicator) indicator.textContent = '硬件';
        } else if (stepIndex === 1) {
            // 第二个位置：软件
            item.setAttribute('data-category', 'software');
            item.className = item.className.replace(/terminal|transmission|service/, 'software');
            const indicator = item.querySelector('.layer-indicator');
            if (indicator) indicator.textContent = '软件';
        } else if (stepIndex === 2) {
            // 第三个位置：互联网服务
            item.setAttribute('data-category', 'internet');
            item.className = item.className.replace(/terminal|transmission|service/, 'internet');
            const indicator = item.querySelector('.layer-indicator');
            if (indicator) indicator.textContent = '互联网服务';
        } else if (stepIndex === 3) {
            // 第四个位置：AI服务
            item.setAttribute('data-category', 'ai');
            item.className = item.className.replace(/terminal|transmission|service/, 'ai');
            const indicator = item.querySelector('.layer-indicator');
            if (indicator) indicator.textContent = 'AI服务';
        }
    });

    // 自动为所有项目添加行业标签
    document.querySelectorAll('.step > div').forEach(item => {
        const hardwareName = item.querySelector('.hardware-name')?.textContent;
        if (hardwareName && industryMapping[hardwareName]) {
            item.setAttribute('data-industry', industryMapping[hardwareName]);
        }
    });

    // 初始化默认状态
    currentCategory = 'hardware';
    currentIndustry = 'all';

    // 设置默认按钮状态
    const defaultButton = document.querySelector('.tab-button');
    defaultButton.classList.add('active');

    // 确保所有子菜单都隐藏
    document.querySelectorAll('.sub-menu').forEach(menu => {
        menu.style.display = 'none';
    });

    // 应用默认过滤
    filterContent();
});
</script>

<!-- 学术分析总结部分 -->
<div style="max-width:1200px;margin:50px auto;padding:30px;background:#1a1a1a;border-radius:12px;border:1px solid #333;">
    <h2 style="color:#4facfe;text-align:center;margin-bottom:30px;font-size:2em;">硬件发展历程的学术总结与判断</h2>

    <div style="display:grid;grid-template-columns:1fr 1fr;gap:30px;margin-bottom:40px;">
        <div style="background:#252545;padding:25px;border-radius:8px;">
            <h3 style="color:#FF6B6B;margin-bottom:15px;">技术演进的基本规律</h3>
            <div style="color:#ccc;line-height:1.6;font-size:14px;">
                <p><strong>1. 摩尔定律的验证与延续</strong><br>
                从1965年Gordon Moore提出至今，芯片性能每18-24个月翻倍的规律基本得到验证，推动了整个计算机产业的指数级发展。</p>

                <p><strong>2. 技术收敛与标准化</strong><br>
                每个时代都经历从多样化竞争到标准统一的过程：IBM PC确立了个人电脑标准，TCP/IP成为网络协议标准，x86架构统治了PC市场。</p>

                <p><strong>3. 性能-功耗-成本的平衡演进</strong><br>
                技术发展始终在追求性能提升的同时，平衡功耗和成本。移动设备的兴起更加突出了这一平衡的重要性。</p>
            </div>
        </div>

        <div style="background:#252545;padding:25px;border-radius:8px;">
            <h3 style="color:#4ECDC4;margin-bottom:15px;">产业发展的周期性特征</h3>
            <div style="color:#ccc;line-height:1.6;font-size:14px;">
                <p><strong>1. 技术S曲线</strong><br>
                每项核心技术都遵循缓慢起步→快速发展→成熟稳定的S曲线。电子管→晶体管→集成电路→超大规模集成电路的演进体现了这一规律。</p>

                <p><strong>2. 破坏性创新的周期</strong><br>
                约每15-20年出现一次重大的破坏性创新：1970s微处理器、1990s互联网、2010s移动互联网、2020s人工智能。</p>

                <p><strong>3. 平台化发展趋势</strong><br>
                成功的技术往往发展成为平台：PC平台、互联网平台、移动平台、云计算平台，每个平台都催生了庞大的生态系统。</p>
            </div>
        </div>
    </div>

    <div style="background:#1a1a2e;padding:25px;border-radius:8px;margin-bottom:30px;">
        <h3 style="color:#45B7D1;margin-bottom:20px;text-align:center;">三阶段发展的深层逻辑</h3>
        <div style="display:grid;grid-template-columns:1fr 1fr 1fr;gap:20px;">
            <div style="text-align:center;">
                <h4 style="color:#FF6B6B;margin-bottom:10px;">计算机时代 (1946-1995)</h4>
                <div style="color:#ccc;font-size:13px;line-height:1.5;">
                    <p><strong>核心驱动：</strong>算力民主化</p>
                    <p><strong>技术特征：</strong>从集中到分散</p>
                    <p><strong>商业模式：</strong>硬件销售为主</p>
                    <p><strong>关键突破：</strong>微处理器、个人电脑</p>
                    <p><strong>社会影响：</strong>计算能力普及</p>
                </div>
            </div>
            <div style="text-align:center;">
                <h4 style="color:#4ECDC4;margin-bottom:10px;">互联网时代 (1995-2010)</h4>
                <div style="color:#ccc;font-size:13px;line-height:1.5;">
                    <p><strong>核心驱动：</strong>连接价值最大化</p>
                    <p><strong>技术特征：</strong>网络效应显现</p>
                    <p><strong>商业模式：</strong>服务和广告</p>
                    <p><strong>关键突破：</strong>万维网、移动互联网</p>
                    <p><strong>社会影响：</strong>信息获取革命</p>
                </div>
            </div>
            <div style="text-align:center;">
                <h4 style="color:#45B7D1;margin-bottom:10px;">AI与云时代 (2010-至今)</h4>
                <div style="color:#ccc;font-size:13px;line-height:1.5;">
                    <p><strong>核心驱动：</strong>数据智能化</p>
                    <p><strong>技术特征：</strong>算法与数据融合</p>
                    <p><strong>商业模式：</strong>平台和订阅</p>
                    <p><strong>关键突破：</strong>深度学习、大模型</p>
                    <p><strong>社会影响：</strong>智能决策辅助</p>
                </div>
            </div>
        </div>
    </div>

    <div style="background:#252545;padding:25px;border-radius:8px;margin-bottom:30px;">
        <h3 style="color:#f5576c;margin-bottom:20px;">终端-传输-服务三层架构的演进逻辑</h3>
        <div style="color:#ccc;line-height:1.6;font-size:14px;">
            <div style="display:grid;grid-template-columns:1fr 1fr 1fr;gap:20px;">
                <div>
                    <h4 style="color:#FF6B6B;margin-bottom:10px;">终端层演进</h4>
                    <p><strong>胖终端时代：</strong>PC集成了计算、存储、显示等全部功能，是完整的计算系统。</p>
                    <p><strong>瘦终端时代：</strong>终端主要作为网络接入点，核心功能在服务器端实现。</p>
                    <p><strong>智能终端时代：</strong>终端重新"变胖"，但重点是AI能力和传感器集成，成为智能交互入口。</p>
                </div>
                <div>
                    <h4 style="color:#4ECDC4;margin-bottom:10px;">传输层演进</h4>
                    <p><strong>本地连接：</strong>主要解决计算机内部和近距离设备连接问题。</p>
                    <p><strong>全球互联：</strong>重点是带宽扩展和覆盖范围，实现全球信息流动。</p>
                    <p><strong>泛在网络：</strong>追求无处不在的高速连接，支撑实时AI计算和海量数据传输。</p>
                </div>
                <div>
                    <h4 style="color:#45B7D1;margin-bottom:10px;">服务层演进</h4>
                    <p><strong>集中式服务：</strong>大型机提供集中计算服务，资源昂贵且稀缺。</p>
                    <p><strong>分布式服务：</strong>服务器集群提供可扩展的网络服务，资源相对丰富。</p>
                    <p><strong>智能化服务：</strong>云计算提供按需的智能服务，AI能力成为核心竞争力。</p>
                </div>
            </div>
        </div>
    </div>

    <div style="background:#1a1a2e;padding:25px;border-radius:8px;margin-bottom:30px;">
        <h3 style="color:#00f2fe;margin-bottom:20px;">关键技术突破的时间节点分析</h3>
        <div style="color:#ccc;line-height:1.6;font-size:14px;">
            <div style="display:grid;grid-template-columns:1fr 1fr;gap:25px;">
                <div>
                    <h4 style="color:#FF6B6B;margin-bottom:15px;">基础技术突破</h4>
                    <ul style="margin:0;padding-left:20px;">
                        <li><strong>1948年 晶体管：</strong>固态电子器件，奠定现代电子学基础</li>
                        <li><strong>1958年 集成电路：</strong>微电子革命开端，摩尔定律起点</li>
                        <li><strong>1971年 微处理器：</strong>计算能力小型化，个人电脑成为可能</li>
                        <li><strong>1973年 以太网：</strong>局域网标准，网络时代基础设施</li>
                        <li><strong>1989年 万维网：</strong>信息共享革命，互联网普及关键</li>
                    </ul>
                </div>
                <div>
                    <h4 style="color:#4ECDC4;margin-bottom:15px;">应用突破时点</h4>
                    <ul style="margin:0;padding-left:20px;">
                        <li><strong>1981年 IBM PC：</strong>个人电脑标准化，计算机进入家庭</li>
                        <li><strong>1995年 Windows 95：</strong>图形界面普及，用户体验革命</li>
                        <li><strong>2007年 iPhone：</strong>移动互联网爆发，触控交互标准</li>
                        <li><strong>2012年 AlexNet：</strong>深度学习突破，AI实用化开端</li>
                        <li><strong>2023年 ChatGPT：</strong>大语言模型普及，AI民主化</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div style="background:#252545;padding:25px;border-radius:8px;">
        <h3 style="color:#f093fb;margin-bottom:20px;">未来发展趋势的学术判断</h3>
        <div style="color:#ccc;line-height:1.6;font-size:14px;">
            <div style="display:grid;grid-template-columns:1fr 1fr;gap:25px;">
                <div>
                    <h4 style="color:#FF6B6B;margin-bottom:15px;">技术发展趋势</h4>
                    <p><strong>1. 后摩尔定律时代：</strong>物理极限逼近，转向3D堆叠、新材料、量子计算等新路径。</p>
                    <p><strong>2. 异构计算兴起：</strong>CPU+GPU+AI芯片+FPGA的异构架构成为主流。</p>
                    <p><strong>3. 边缘计算普及：</strong>计算能力向边缘下沉，实现实时响应和隐私保护。</p>
                    <p><strong>4. 软硬件协同设计：</strong>针对特定应用的软硬件一体化优化。</p>
                </div>
                <div>
                    <h4 style="color:#4ECDC4;margin-bottom:15px;">产业发展预测</h4>
                    <p><strong>1. AI原生架构：</strong>从AI适配现有硬件转向为AI设计的专用架构。</p>
                    <p><strong>2. 可持续计算：</strong>绿色计算、能效优化成为设计核心考量。</p>
                    <p><strong>3. 人机融合界面：</strong>脑机接口、AR/VR等新交互方式逐步成熟。</p>
                    <p><strong>4. 自主进化系统：</strong>具备自我学习和优化能力的计算系统。</p>
                </div>
            </div>

            <div style="margin-top:25px;padding:20px;background:#1a1a2e;border-radius:6px;border-left:4px solid #4facfe;">
                <h4 style="color:#4facfe;margin-bottom:15px;">核心学术观点</h4>
                <p style="margin-bottom:10px;"><strong>观点一：</strong>计算机硬件发展遵循"集中→分散→重新集中"的螺旋式上升规律，每次集中都在更高层次上实现。</p>
                <p style="margin-bottom:10px;"><strong>观点二：</strong>技术突破往往来自跨领域融合，如AI芯片融合了计算机架构、算法优化、材料科学等多个领域。</p>
                <p style="margin-bottom:10px;"><strong>观点三：</strong>硬件发展的终极目标是"透明化"——让用户感受不到硬件的存在，专注于应用和体验。</p>
                <p><strong>观点四：</strong>未来的计算范式将从"冯·诺依曼架构"向"神经形态计算"等新架构演进，实现更高的能效比和智能化水平。</p>
            </div>
        </div>
    </div>
</div>

</body>
</html>

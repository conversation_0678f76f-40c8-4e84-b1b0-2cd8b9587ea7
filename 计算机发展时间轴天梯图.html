<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<title>计算机发展时间轴天梯图 - 三阶段硬件演进</title>
<style>
    body{font-family:'Microsoft YaHei',<PERSON><PERSON>,sans-serif;margin:0;background:#111;color:#eee;}
    h1{text-align:center;margin:20px 0;color:#fff;font-size:2.2em;}
    .subtitle{text-align:center;margin:10px 0;color:#ccc;font-size:16px;}
    
    .phase-description{max-width:1200px;margin:20px auto;padding:15px;background:#1a1a1a;border-radius:8px;display:flex;justify-content:space-around;}
    .phase{flex:1;padding:0 15px;border-left:1px solid #444;}
    .phase:first-child{border-left:none;}
    .phase h3{font-size:14px;color:#eee;margin-top:0;margin-bottom:8px;}
    .phase p{font-size:12px;color:#ccc;line-height:1.4;margin:5px 0;}
    .phase .era1{color:#FF6B6B;}
    .phase .era2{color:#4ECDC4;}
    .phase .era3{color:#45B7D1;}
    
    .tabs{text-align:center;margin:20px 0;}
    .tab-button{background:#333;color:#fff;border:1px solid #555;padding:8px 16px;cursor:pointer;margin:0 4px;border-radius:5px;}
    .tab-button.active{background:#555;border-color:#777;}
    
    .legend{margin:20px auto;width:90%;max-width:600px;font-size:13px;display:flex;justify-content:space-around;}
    .legend span{padding:4px 8px;border-radius:4px;margin-right:6px;}
    .legend .era1{background:#FF6B6B;color:#fff;}
    .legend .era2{background:#4ECDC4;color:#fff;}
    .legend .era3{background:#45B7D1;color:#fff;}
    
    .ladder{display:flex;flex-direction:column-reverse;align-items:center;width:100%;}
    .year-bar{width:90%;max-width:1200px;display:flex;border-top:1px solid #444;margin-bottom:2px;}
    .year-label{width:80px;flex-shrink:0;text-align:center;font-weight:bold;padding:6px 0;background:#222;}
    .step{display:flex;flex:1;height:auto;min-height:70px;overflow:hidden;}
    .step>div{flex:1;display:flex;flex-direction:column;justify-content:center;align-items:center;font-size:12px;padding:8px 4px;border-left:1px solid #333;position:relative;}
    
    .step .era1{background:#2d1b1b;border-left:3px solid #FF6B6B;}
    .step .era2{background:#1b2d2d;border-left:3px solid #4ECDC4;}
    .step .era3{background:#1b1b2d;border-left:3px solid #45B7D1;}
    
    .step .terminal{background-color:#003300;}
    .step .transmission{background-color:#330033;}
    .step .service{background-color:#000033;}
    
    .step>div.active{display:flex;}
    .step>div{display:none;}
    .step>div.show-all{display:flex;}
    
    .description{font-size:11px;color:#bbb;margin-top:4px;font-style:italic;}
    .hardware-name{font-weight:bold;color:#fff;margin-bottom:2px;}
    .layer-indicator{position:absolute;top:2px;right:2px;font-size:8px;padding:1px 3px;border-radius:2px;background:#666;color:#fff;}
    
    @media (max-width:1000px){
        .year-bar{width:95%;}
        .step>div{font-size:11px;padding:6px 2px;}
        .phase-description{flex-direction:column;padding:10px;}
        .phase{border-left:none;border-top:1px solid #444;padding:10px 0;}
        .phase:first-child{border-top:none;}
    }
</style>
</head>
<body>
<h1>计算机发展时间轴天梯图</h1>
<div class="subtitle">三阶段硬件演进：计算机时代 → 互联网时代 → AI与云时代</div>

<div class="phase-description">
    <div class="phase">
        <h3><span class="era1">第一阶段: 计算机时代 (约1970s - 1995)</span></h3>
        <p><strong>核心:</strong> 计算的诞生与普及</p>
        <p><strong>① 终端:</strong> 个人电脑(PC) - 一体化的计算与交互中心</p>
        <p><strong>② 传输:</strong> 本地化连接 - 主板总线、串口/并口、局域网</p>
        <p><strong>③ 服务:</strong> 大型机/小型机 - 集中的、昂贵的计算资源</p>
    </div>
    <div class="phase">
        <h3><span class="era2">第二阶段: 互联网时代 (约1995 - 2010)</span></h3>
        <p><strong>核心:</strong> 连接的价值</p>
        <p><strong>① 终端:</strong> 多样化接入设备 - PC、笔记本、功能手机</p>
        <p><strong>② 传输:</strong> 全球互联 - Modem、DSL、光纤、Wi-Fi、2G/3G</p>
        <p><strong>③ 服务:</strong> 分布式服务器/数据中心 - 网站和应用的载体</p>
    </div>
    <div class="phase">
        <h3><span class="era3">第三阶段: AI与云时代 (约2010 - 至今)</span></h3>
        <p><strong>核心:</strong> 数据的智能</p>
        <p><strong>① 终端:</strong> 泛在智能终端 - 智能手机、IoT、可穿戴设备</p>
        <p><strong>② 传输:</strong> 高速泛在网络 - 5G、千兆光纤、Wi-Fi 6</p>
        <p><strong>③ 服务:</strong> 云数据中心/边缘计算 - 智能与算力服务</p>
    </div>
</div>

<div class="tabs">
    <button class="tab-button active" onclick="filterByCategory('all', this)">显示全部</button>
    <button class="tab-button" onclick="filterByCategory('terminal', this)">① 终端</button>
    <button class="tab-button" onclick="filterByCategory('transmission', this)">② 传输</button>
    <button class="tab-button" onclick="filterByCategory('service', this)">③ 服务</button>
</div>

<div class="legend">
    <span class="era1">第一阶段：计算机时代</span>
    <span class="era2">第二阶段：互联网时代</span>
    <span class="era3">第三阶段：AI与云时代</span>
</div>

<div class="ladder">
<!-- 2024 -->
<div class="year-bar">
    <div class="year-label">2024</div>
    <div class="step">
        <div class="era3 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Apple M4 芯片</div>
            <div class="description">集成强大神经引擎的最新SoC</div>
        </div>
        <div class="era3 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">Wi-Fi 7 标准</div>
            <div class="description">理论速度高达46 Gbps</div>
        </div>
        <div class="era3 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Nvidia H200</div>
            <div class="description">AI大模型训练顶级显卡</div>
        </div>
    </div>
</div>

<!-- 2023 -->
<div class="year-bar">
    <div class="year-label">2023</div>
    <div class="step">
        <div class="era3 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Apple Vision Pro</div>
            <div class="description">空间计算设备，混合现实新时代</div>
        </div>
        <div class="era3 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">800G 光模块</div>
            <div class="description">满足AI集群极端带宽需求</div>
        </div>
        <div class="era3 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">ChatGPT/GPT-4</div>
            <div class="description">大语言模型商业化应用</div>
        </div>
    </div>
</div>

<!-- 2020 -->
<div class="year-bar">
    <div class="year-label">2020</div>
    <div class="step">
        <div class="era3 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Apple M1 芯片</div>
            <div class="description">ARM架构进入PC市场</div>
        </div>
        <div class="era3 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">5G 网络商用</div>
            <div class="description">高带宽、低延迟移动网络</div>
        </div>
        <div class="era3 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Nvidia A100</div>
            <div class="description">AI、数据分析专用GPU</div>
        </div>
    </div>
</div>

<!-- 2016 -->
<div class="year-bar">
    <div class="year-label">2016</div>
    <div class="step">
        <div class="era3 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">AlphaGo</div>
            <div class="description">AI发展史上的里程碑</div>
        </div>
        <div class="era3 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">100G 光模块</div>
            <div class="description">数据中心网络主流方案</div>
        </div>
        <div class="era3 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Nvidia DGX-1</div>
            <div class="description">首台深度学习超级计算机</div>
        </div>
    </div>
</div>

<!-- 2010 -->
<div class="year-bar">
    <div class="year-label">2010</div>
    <div class="step">
        <div class="era3 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">iPad</div>
            <div class="description">开创平板电脑市场</div>
        </div>
        <div class="era2 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">4G LTE</div>
            <div class="description">移动宽带网络</div>
        </div>
        <div class="era2 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">云计算普及</div>
            <div class="description">AWS、Azure等云服务</div>
        </div>
    </div>
</div>

<!-- 2007 -->
<div class="year-bar">
    <div class="year-label">2007</div>
    <div class="step">
        <div class="era2 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">iPhone</div>
            <div class="description">重新定义智能手机</div>
        </div>
        <div class="era2 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">3G 网络</div>
            <div class="description">移动设备真正上网</div>
        </div>
        <div class="era2 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Amazon EC2</div>
            <div class="description">弹性云计算服务</div>
        </div>
    </div>
</div>

<!-- 2001 -->
<div class="year-bar">
    <div class="year-label">2001</div>
    <div class="step">
        <div class="era2 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">iPod</div>
            <div class="description">数字音乐播放器革命</div>
        </div>
        <div class="era2 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">Wi-Fi 802.11b</div>
            <div class="description">无线局域网普及</div>
        </div>
        <div class="era2 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">机架式服务器</div>
            <div class="description">标准化数据中心</div>
        </div>
    </div>
</div>

<!-- 1995 -->
<div class="year-bar">
    <div class="year-label">1995</div>
    <div class="step">
        <div class="era2 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Windows 95</div>
            <div class="description">图形界面操作系统普及</div>
        </div>
        <div class="era2 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">调制解调器</div>
            <div class="description">家庭上网的标志</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Pentium 处理器</div>
            <div class="description">PC性能大幅提升</div>
        </div>
    </div>
</div>

<!-- 1984 -->
<div class="year-bar">
    <div class="year-label">1984</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Apple Macintosh</div>
            <div class="description">首款成功的GUI个人电脑</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">以太网标准</div>
            <div class="description">局域网连接技术</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">IBM System/360</div>
            <div class="description">兼容性计算机系列</div>
        </div>
    </div>
</div>

<!-- 1981 -->
<div class="year-bar">
    <div class="year-label">1981</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">IBM PC</div>
            <div class="description">个人电脑标准确立</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">串口/并口</div>
            <div class="description">外设连接标准</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">MS-DOS</div>
            <div class="description">PC操作系统基础</div>
        </div>
    </div>
</div>

<!-- 1977 -->
<div class="year-bar">
    <div class="year-label">1977</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Apple II</div>
            <div class="description">首款大规模成功的PC</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">主板总线</div>
            <div class="description">计算机内部数据通道</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">小型机 PDP-11</div>
            <div class="description">多用户计算系统</div>
        </div>
    </div>
</div>

<!-- 1971 -->
<div class="year-bar">
    <div class="year-label">1971</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">8英寸软盘</div>
            <div class="description">首个便携式存储介质</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">ARPANET</div>
            <div class="description">互联网前身网络</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Intel 4004</div>
            <div class="description">首款商用微处理器</div>
        </div>
    </div>
</div>
</div>

<script>
function filterByCategory(category, button) {
    const allItems = document.querySelectorAll('.step > div');
    const allButtons = document.querySelectorAll('.tab-button');
    
    // 移除所有按钮的active类
    allButtons.forEach(btn => btn.classList.remove('active'));
    button.classList.add('active');
    
    if (category === 'all') {
        allItems.forEach(item => {
            item.classList.remove('active');
            item.classList.add('show-all');
        });
    } else {
        allItems.forEach(item => {
            item.classList.remove('show-all');
            if (item.dataset.category === category) {
                item.classList.add('active');
            } else {
                item.classList.remove('active');
            }
        });
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('计算机发展时间轴天梯图加载完成');
});
</script>

</body>
</html>

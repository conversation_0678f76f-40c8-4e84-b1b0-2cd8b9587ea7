<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<title>计算机发展时间轴天梯图 - 三阶段硬件演进</title>
<style>
    body{font-family:'Microsoft YaHei',<PERSON><PERSON>,sans-serif;margin:0;background:#111;color:#eee;}
    h1{text-align:center;margin:20px 0;color:#fff;font-size:2.2em;}
    .subtitle{text-align:center;margin:10px 0;color:#ccc;font-size:16px;}
    
    .phase-description{max-width:1200px;margin:20px auto;padding:15px;background:#1a1a1a;border-radius:8px;display:flex;justify-content:space-around;}
    .phase{flex:1;padding:0 15px;border-left:1px solid #444;}
    .phase:first-child{border-left:none;}
    .phase h3{font-size:14px;color:#eee;margin-top:0;margin-bottom:8px;}
    .phase p{font-size:12px;color:#ccc;line-height:1.4;margin:5px 0;}
    .phase .era1{color:#FF6B6B;}
    .phase .era2{color:#4ECDC4;}
    .phase .era3{color:#45B7D1;}
    
    .tabs{text-align:center;margin:20px 0;}
    .tab-button{background:#333;color:#fff;border:1px solid #555;padding:8px 16px;cursor:pointer;margin:0 4px;border-radius:5px;}
    .tab-button.active{background:#555;border-color:#777;}
    
    .legend{margin:20px auto;width:90%;max-width:600px;font-size:13px;display:flex;justify-content:space-around;}
    .legend span{padding:4px 8px;border-radius:4px;margin-right:6px;}
    .legend .era1{background:#FF6B6B;color:#fff;}
    .legend .era2{background:#4ECDC4;color:#fff;}
    .legend .era3{background:#45B7D1;color:#fff;}
    
    .ladder{display:flex;flex-direction:column-reverse;align-items:center;width:100%;}
    .year-bar{width:90%;max-width:1200px;display:flex;border-top:1px solid #444;margin-bottom:2px;}
    .year-label{width:80px;flex-shrink:0;text-align:center;font-weight:bold;padding:6px 0;background:#222;}
    .step{display:flex;flex:1;height:auto;min-height:70px;overflow:hidden;}
    .step>div{flex:1;display:flex;flex-direction:column;justify-content:center;align-items:center;font-size:12px;padding:8px 4px;border-left:1px solid #333;position:relative;}
    
    .step .era1{background:#2d1b1b;border-left:3px solid #FF6B6B;}
    .step .era2{background:#1b2d2d;border-left:3px solid #4ECDC4;}
    .step .era3{background:#1b1b2d;border-left:3px solid #45B7D1;}
    
    .step .terminal{background-color:#003300;}
    .step .transmission{background-color:#330033;}
    .step .service{background-color:#000033;}
    
    .step>div.active{display:flex;}
    .step>div{display:none;}
    .step>div.show-all{display:flex;}
    
    .description{font-size:11px;color:#bbb;margin-top:4px;font-style:italic;}
    .hardware-name{font-weight:bold;color:#fff;margin-bottom:2px;}
    .layer-indicator{position:absolute;top:2px;right:2px;font-size:8px;padding:1px 3px;border-radius:2px;background:#666;color:#fff;}
    
    @media (max-width:1000px){
        .year-bar{width:95%;}
        .step>div{font-size:11px;padding:6px 2px;}
        .phase-description{flex-direction:column;padding:10px;}
        .phase{border-left:none;border-top:1px solid #444;padding:10px 0;}
        .phase:first-child{border-top:none;}
    }
</style>
</head>
<body>
<h1>计算机发展时间轴天梯图</h1>
<div class="subtitle">三阶段硬件演进：计算机时代 → 互联网时代 → AI与云时代</div>

<div class="phase-description">
    <div class="phase">
        <h3><span class="era1">第一阶段: 计算机时代 (约1970s - 1995)</span></h3>
        <p><strong>核心:</strong> 计算的诞生与普及</p>
        <p><strong>① 终端:</strong> 个人电脑(PC) - 一体化的计算与交互中心</p>
        <p><strong>② 传输:</strong> 本地化连接 - 主板总线、串口/并口、局域网</p>
        <p><strong>③ 服务:</strong> 大型机/小型机 - 集中的、昂贵的计算资源</p>
    </div>
    <div class="phase">
        <h3><span class="era2">第二阶段: 互联网时代 (约1995 - 2010)</span></h3>
        <p><strong>核心:</strong> 连接的价值</p>
        <p><strong>① 终端:</strong> 多样化接入设备 - PC、笔记本、功能手机</p>
        <p><strong>② 传输:</strong> 全球互联 - Modem、DSL、光纤、Wi-Fi、2G/3G</p>
        <p><strong>③ 服务:</strong> 分布式服务器/数据中心 - 网站和应用的载体</p>
    </div>
    <div class="phase">
        <h3><span class="era3">第三阶段: AI与云时代 (约2010 - 至今)</span></h3>
        <p><strong>核心:</strong> 数据的智能</p>
        <p><strong>① 终端:</strong> 泛在智能终端 - 智能手机、IoT、可穿戴设备</p>
        <p><strong>② 传输:</strong> 高速泛在网络 - 5G、千兆光纤、Wi-Fi 6</p>
        <p><strong>③ 服务:</strong> 云数据中心/边缘计算 - 智能与算力服务</p>
    </div>
</div>

<div class="tabs">
    <button class="tab-button active" onclick="filterByCategory('all', this)">显示全部</button>
    <button class="tab-button" onclick="filterByCategory('terminal', this)">① 终端</button>
    <button class="tab-button" onclick="filterByCategory('transmission', this)">② 传输</button>
    <button class="tab-button" onclick="filterByCategory('service', this)">③ 服务</button>
</div>

<div class="legend">
    <span class="era1">第一阶段：计算机时代</span>
    <span class="era2">第二阶段：互联网时代</span>
    <span class="era3">第三阶段：AI与云时代</span>
</div>

<div class="ladder">
<!-- 2024 -->
<div class="year-bar">
    <div class="year-label">2024</div>
    <div class="step">
        <div class="era3 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Apple M4 芯片</div>
            <div class="description">3nm工艺，集成强大神经引擎的最新SoC，支持AI加速</div>
        </div>
        <div class="era3 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">Wi-Fi 7 (802.11be)</div>
            <div class="description">理论速度高达46 Gbps，支持320MHz带宽</div>
        </div>
        <div class="era3 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Nvidia H200</div>
            <div class="description">141GB HBM3e内存，AI大模型训练顶级显卡</div>
        </div>
    </div>
</div>

<!-- 2023 -->
<div class="year-bar">
    <div class="year-label">2023</div>
    <div class="step">
        <div class="era3 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Apple Vision Pro</div>
            <div class="description">M2+R1双芯片，空间计算设备，混合现实新时代</div>
        </div>
        <div class="era3 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">800G 光模块</div>
            <div class="description">满足AI集群极端带宽需求，数据中心骨干网络</div>
        </div>
        <div class="era3 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">ChatGPT/GPT-4</div>
            <div class="description">大语言模型商业化应用，AI普及的里程碑</div>
        </div>
    </div>
</div>

<!-- 2022 -->
<div class="year-bar">
    <div class="year-label">2022</div>
    <div class="step">
        <div class="era3 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Apple M2 芯片</div>
            <div class="description">5nm工艺，第二代自研SoC，性能进一步提升</div>
        </div>
        <div class="era3 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">CXL 2.0</div>
            <div class="description">计算快速链路，连接CPU、内存和加速器</div>
        </div>
        <div class="era3 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Nvidia H100</div>
            <div class="description">Hopper架构，80GB HBM3，AI训练和推理顶级性能</div>
        </div>
    </div>
</div>

<!-- 2021 -->
<div class="year-bar">
    <div class="year-label">2021</div>
    <div class="step">
        <div class="era3 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Apple M1 Max/Pro</div>
            <div class="description">5nm工艺，高性能SoC，用于MacBook Pro</div>
        </div>
        <div class="era3 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">DDR5 SDRAM</div>
            <div class="description">新一代内存标准，4800-8400 MT/s传输速率</div>
        </div>
        <div class="era3 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Google TPU v4</div>
            <div class="description">谷歌第四代张量处理单元，AI训练专用</div>
        </div>
    </div>
</div>

<!-- 2020 -->
<div class="year-bar">
    <div class="year-label">2020</div>
    <div class="step">
        <div class="era3 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Apple M1 芯片</div>
            <div class="description">5nm工艺，ARM架构进入PC市场，统一内存架构</div>
        </div>
        <div class="era3 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">5G 网络商用</div>
            <div class="description">高带宽、低延迟移动网络，峰值速度20Gbps</div>
        </div>
        <div class="era3 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Nvidia A100</div>
            <div class="description">Ampere架构，40GB HBM2，AI、数据分析专用GPU</div>
        </div>
    </div>
</div>

<!-- 2019 -->
<div class="year-bar">
    <div class="year-label">2019</div>
    <div class="step">
        <div class="era3 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">AMD Ryzen 3000</div>
            <div class="description">7nm工艺，Zen 2架构，与英特尔激烈竞争</div>
        </div>
        <div class="era3 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">Wi-Fi 6 (802.11ax)</div>
            <div class="description">新一代Wi-Fi标准，9.6Gbps理论速度</div>
        </div>
        <div class="era3 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Google Edge TPU</div>
            <div class="description">边缘设备AI推理专用芯片，4TOPS算力</div>
        </div>
    </div>
</div>

<!-- 2018 -->
<div class="year-bar">
    <div class="year-label">2018</div>
    <div class="step">
        <div class="era3 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">iPhone XS (A12 Bionic)</div>
            <div class="description">7nm工艺，首个7nm手机芯片，神经引擎</div>
        </div>
        <div class="era3 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">USB 3.2</div>
            <div class="description">20Gbps传输速度，向下兼容</div>
        </div>
        <div class="era3 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Nvidia Turing (RTX 2080)</div>
            <div class="description">实时光线追踪，DLSS技术，12nm工艺</div>
        </div>
    </div>
</div>

<!-- 2017 -->
<div class="year-bar">
    <div class="year-label">2017</div>
    <div class="step">
        <div class="era3 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">iPhone X (A11 Bionic)</div>
            <div class="description">Face ID，神经引擎，推动移动AI应用</div>
        </div>
        <div class="era3 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">Thunderbolt 3</div>
            <div class="description">40Gbps带宽，USB-C接口，支持外接显卡</div>
        </div>
        <div class="era3 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Nvidia Tesla V100</div>
            <div class="description">Volta架构，Tensor Core，极大加速AI训练</div>
        </div>
    </div>
</div>

<!-- 2016 -->
<div class="year-bar">
    <div class="year-label">2016</div>
    <div class="step">
        <div class="era3 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">AlphaGo</div>
            <div class="description">击败世界围棋冠军，AI发展史上的里程碑</div>
        </div>
        <div class="era3 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">100G 光模块 (QSFP28)</div>
            <div class="description">数据中心网络主流方案，支持100G以太网</div>
        </div>
        <div class="era3 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Nvidia DGX-1</div>
            <div class="description">首台深度学习超级计算机，8个Tesla P100</div>
        </div>
    </div>
</div>

<!-- 2015 -->
<div class="year-bar">
    <div class="year-label">2015</div>
    <div class="step">
        <div class="era3 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Apple Watch</div>
            <div class="description">定义现代智能手表，可穿戴设备新时代</div>
        </div>
        <div class="era3 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">USB Type-C</div>
            <div class="description">统一接口标准，支持数据、视频、电源</div>
        </div>
        <div class="era3 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Google TPU v1</div>
            <div class="description">谷歌首款AI专用芯片，用于AlphaGo</div>
        </div>
    </div>
</div>

<!-- 2014 -->
<div class="year-bar">
    <div class="year-label">2014</div>
    <div class="step">
        <div class="era3 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Intel Broadwell</div>
            <div class="description">14nm工艺，英特尔首次进入14nm时代</div>
        </div>
        <div class="era3 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">802.11ac Wave 2</div>
            <div class="description">千兆Wi-Fi，MU-MIMO技术</div>
        </div>
        <div class="era3 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">AWS Lambda</div>
            <div class="description">无服务器计算，开创FaaS模式</div>
        </div>
    </div>
</div>

<!-- 2013 -->
<div class="year-bar">
    <div class="year-label">2013</div>
    <div class="step">
        <div class="era3 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">iPhone 5S (A7)</div>
            <div class="description">首款64位手机处理器，Touch ID指纹识别</div>
        </div>
        <div class="era3 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">802.11ac</div>
            <div class="description">5GHz频段，理论速度1.3Gbps</div>
        </div>
        <div class="era3 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">PlayStation 4</div>
            <div class="description">AMD APU，8GB GDDR5，游戏主机新时代</div>
        </div>
    </div>
</div>

<!-- 2012 -->
<div class="year-bar">
    <div class="year-label">2012</div>
    <div class="step">
        <div class="era3 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Raspberry Pi</div>
            <div class="description">35美元单板计算机，推动创客运动</div>
        </div>
        <div class="era3 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">Thunderbolt 2</div>
            <div class="description">20Gbps带宽，支持4K显示器</div>
        </div>
        <div class="era3 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">AlexNet</div>
            <div class="description">深度学习突破，ImageNet竞赛冠军</div>
        </div>
    </div>
</div>

<!-- 2011 -->
<div class="year-bar">
    <div class="year-label">2011</div>
    <div class="step">
        <div class="era3 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">iPad 2</div>
            <div class="description">A5双核处理器，更薄更轻的平板</div>
        </div>
        <div class="era3 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">USB 3.0 普及</div>
            <div class="description">5Gbps传输速度，是USB 2.0的十倍</div>
        </div>
        <div class="era3 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">IBM Watson</div>
            <div class="description">AI系统，在智力竞赛中击败人类冠军</div>
        </div>
    </div>
</div>

<!-- 2010 -->
<div class="year-bar">
    <div class="year-label">2010</div>
    <div class="step">
        <div class="era3 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">iPad</div>
            <div class="description">A4处理器，开创平板电脑市场</div>
        </div>
        <div class="era2 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">4G LTE</div>
            <div class="description">移动宽带网络，理论速度100Mbps</div>
        </div>
        <div class="era2 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">云计算普及</div>
            <div class="description">AWS、Azure等云服务成为主流</div>
        </div>
    </div>
</div>

<!-- 2009 -->
<div class="year-bar">
    <div class="year-label">2009</div>
    <div class="step">
        <div class="era2 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Intel Nehalem</div>
            <div class="description">45nm工艺，集成内存控制器，性能大幅提升</div>
        </div>
        <div class="era2 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">USB 3.0 标准</div>
            <div class="description">SuperSpeed USB，5Gbps传输速度</div>
        </div>
        <div class="era2 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">SSD 普及</div>
            <div class="description">固态硬盘开始普及，存储性能革命</div>
        </div>
    </div>
</div>

<!-- 2008 -->
<div class="year-bar">
    <div class="year-label">2008</div>
    <div class="step">
        <div class="era2 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">App Store</div>
            <div class="description">移动应用生态系统，改变软件分发模式</div>
        </div>
        <div class="era2 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">DisplayPort 1.0</div>
            <div class="description">新一代显示接口标准，支持高分辨率</div>
        </div>
        <div class="era2 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Intel Atom</div>
            <div class="description">低功耗处理器，专为上网本设计</div>
        </div>
    </div>
</div>

<!-- 2007 -->
<div class="year-bar">
    <div class="year-label">2007</div>
    <div class="step">
        <div class="era2 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">iPhone</div>
            <div class="description">重新定义智能手机，多点触控革命</div>
        </div>
        <div class="era2 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">3G 网络 (HSPA)</div>
            <div class="description">移动设备真正上网，理论速度14.4Mbps</div>
        </div>
        <div class="era2 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Amazon EC2</div>
            <div class="description">弹性云计算服务，IaaS模式开创</div>
        </div>
    </div>
</div>

<!-- 2006 -->
<div class="year-bar">
    <div class="year-label">2006</div>
    <div class="step">
        <div class="era2 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Intel Core 2 Duo</div>
            <div class="description">65nm工艺，双核处理器，性能功耗平衡</div>
        </div>
        <div class="era2 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">SATA 3.0</div>
            <div class="description">6Gbps传输速度，支持高速SSD</div>
        </div>
        <div class="era2 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Amazon S3</div>
            <div class="description">云存储服务，按需付费存储模式</div>
        </div>
    </div>
</div>

<!-- 2005 -->
<div class="year-bar">
    <div class="year-label">2005</div>
    <div class="step">
        <div class="era2 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">YouTube</div>
            <div class="description">在线视频分享平台，改变内容消费方式</div>
        </div>
        <div class="era2 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">PCI Express 2.0</div>
            <div class="description">5GT/s传输速度，显卡接口标准</div>
        </div>
        <div class="era2 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Google MapReduce</div>
            <div class="description">大数据处理模型，分布式计算框架</div>
        </div>
    </div>
</div>

<!-- 2004 -->
<div class="year-bar">
    <div class="year-label">2004</div>
    <div class="step">
        <div class="era2 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Facebook</div>
            <div class="description">社交网络平台，改变人际交流方式</div>
        </div>
        <div class="era2 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">PCI Express 1.0</div>
            <div class="description">2.5GT/s传输速度，取代AGP和PCI</div>
        </div>
        <div class="era2 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">AMD64 架构</div>
            <div class="description">64位x86扩展，被英特尔采纳</div>
        </div>
    </div>
</div>

<!-- 2003 -->
<div class="year-bar">
    <div class="year-label">2003</div>
    <div class="step">
        <div class="era2 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">iTunes Store</div>
            <div class="description">数字音乐商店，改变音乐产业</div>
        </div>
        <div class="era2 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">Wi-Fi 802.11g</div>
            <div class="description">54Mbps无线网络，2.4GHz频段</div>
        </div>
        <div class="era2 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">AMD Opteron</div>
            <div class="description">首款x86-64服务器处理器</div>
        </div>
    </div>
</div>

<!-- 2002 -->
<div class="year-bar">
    <div class="year-label">2002</div>
    <div class="step">
        <div class="era2 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Tablet PC</div>
            <div class="description">微软早期平板电脑尝试，触控笔输入</div>
        </div>
        <div class="era2 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">SATA 1.0</div>
            <div class="description">1.5Gbps串行ATA，取代PATA</div>
        </div>
        <div class="era2 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">刀片服务器</div>
            <div class="description">高密度服务器，提升数据中心效率</div>
        </div>
    </div>
</div>

<!-- 2001 -->
<div class="year-bar">
    <div class="year-label">2001</div>
    <div class="step">
        <div class="era2 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">iPod</div>
            <div class="description">5GB硬盘，"1000首歌装进口袋"</div>
        </div>
        <div class="era2 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">Wi-Fi 802.11b</div>
            <div class="description">11Mbps无线局域网普及</div>
        </div>
        <div class="era2 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Intel Itanium</div>
            <div class="description">64位EPIC架构，高端服务器处理器</div>
        </div>
    </div>
</div>

<!-- 2000 -->
<div class="year-bar">
    <div class="year-label">2000</div>
    <div class="step">
        <div class="era2 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">PlayStation 2</div>
            <div class="description">DVD播放，向下兼容，史上最畅销游戏主机</div>
        </div>
        <div class="era2 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">USB 2.0</div>
            <div class="description">480Mbps高速USB，成为主流接口</div>
        </div>
        <div class="era2 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Intel Pentium 4</div>
            <div class="description">NetBurst架构，高频率设计</div>
        </div>
    </div>
</div>

<!-- 1999 -->
<div class="year-bar">
    <div class="year-label">1999</div>
    <div class="step">
        <div class="era2 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Nvidia GeForce 256</div>
            <div class="description">首款GPU，图形处理单元概念确立</div>
        </div>
        <div class="era2 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">Wi-Fi 802.11a</div>
            <div class="description">54Mbps，5GHz频段无线网络</div>
        </div>
        <div class="era2 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">AMD Athlon</div>
            <div class="description">首个突破1GHz的消费级处理器</div>
        </div>
    </div>
</div>

<!-- 1998 -->
<div class="year-bar">
    <div class="year-label">1998</div>
    <div class="step">
        <div class="era2 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Google 搜索引擎</div>
            <div class="description">PageRank算法，改变信息检索方式</div>
        </div>
        <div class="era2 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">IEEE 1394 (FireWire)</div>
            <div class="description">400Mbps高速串行总线</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Intel Celeron</div>
            <div class="description">低成本处理器，普及PC市场</div>
        </div>
    </div>
</div>

<!-- 1997 -->
<div class="year-bar">
    <div class="year-label">1997</div>
    <div class="step">
        <div class="era2 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Intel Pentium II</div>
            <div class="description">MMX技术，增强多媒体处理能力</div>
        </div>
        <div class="era2 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">AGP 1.0</div>
            <div class="description">专用显卡接口，266MB/s带宽</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">DVD 标准</div>
            <div class="description">数字视频光盘，4.7GB容量</div>
        </div>
    </div>
</div>

<!-- 1996 -->
<div class="year-bar">
    <div class="year-label">1996</div>
    <div class="step">
        <div class="era2 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">3dfx Voodoo</div>
            <div class="description">3D图形加速卡，游戏图形革命</div>
        </div>
        <div class="era2 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">USB 1.0</div>
            <div class="description">通用串行总线，12Mbps传输速度</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Intel Pentium Pro</div>
            <div class="description">32位服务器和工作站处理器</div>
        </div>
    </div>
</div>

<!-- 1995 -->
<div class="year-bar">
    <div class="year-label">1995</div>
    <div class="step">
        <div class="era2 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Windows 95</div>
            <div class="description">32位操作系统，开始菜单和任务栏</div>
        </div>
        <div class="era2 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">56K 调制解调器</div>
            <div class="description">拨号上网最高速度，家庭上网标志</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Netscape Navigator</div>
            <div class="description">图形化网页浏览器，推动WWW普及</div>
        </div>
    </div>
</div>

<!-- 1994 -->
<div class="year-bar">
    <div class="year-label">1994</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Sony PlayStation</div>
            <div class="description">32位游戏主机，3D游戏时代开启</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">Fast Ethernet</div>
            <div class="description">100Mbps以太网标准</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Netscape 成立</div>
            <div class="description">网络浏览器公司，推动互联网商业化</div>
        </div>
    </div>
</div>

<!-- 1993 -->
<div class="year-bar">
    <div class="year-label">1993</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Intel Pentium</div>
            <div class="description">第五代x86处理器，超标量架构</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">Mosaic 浏览器</div>
            <div class="description">首款图形化网页浏览器</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Windows NT</div>
            <div class="description">32位企业级操作系统</div>
        </div>
    </div>
</div>

<!-- 1991 -->
<div class="year-bar">
    <div class="year-label">1991</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">World Wide Web</div>
            <div class="description">Tim Berners-Lee发明，改变信息共享</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">10BASE-T 以太网</div>
            <div class="description">双绞线以太网，10Mbps</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Linux 内核</div>
            <div class="description">Linus Torvalds开发的开源操作系统</div>
        </div>
    </div>
</div>

<!-- 1989 -->
<div class="year-bar">
    <div class="year-label">1989</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Intel 80486</div>
            <div class="description">内置数学协处理器，25-100MHz</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">ISDN</div>
            <div class="description">综合服务数字网，128Kbps</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">NeXTcube</div>
            <div class="description">乔布斯的高端工作站</div>
        </div>
    </div>
</div>

<!-- 1988 -->
<div class="year-bar">
    <div class="year-label">1988</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">VGA 显示标准</div>
            <div class="description">640×480分辨率，256色显示</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">T1 线路</div>
            <div class="description">1.544Mbps专线，企业级连接</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">RAID 技术</div>
            <div class="description">磁盘阵列，提高存储可靠性</div>
        </div>
    </div>
</div>

<!-- 1985 -->
<div class="year-bar">
    <div class="year-label">1985</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Intel 80386</div>
            <div class="description">首款32位x86处理器，虚拟内存</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">CD-ROM</div>
            <div class="description">光盘存储，650MB容量</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Commodore Amiga</div>
            <div class="description">先进多媒体功能，图形工作站</div>
        </div>
    </div>
</div>

<!-- 1984 -->
<div class="year-bar">
    <div class="year-label">1984</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Apple Macintosh</div>
            <div class="description">首款成功的GUI个人电脑，鼠标操作</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">3.5英寸软盘</div>
            <div class="description">1.44MB容量，更可靠的存储</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">DNS 系统</div>
            <div class="description">域名系统，简化网络地址</div>
        </div>
    </div>
</div>

<!-- 1983 -->
<div class="year-bar">
    <div class="year-label">1983</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Apple Lisa</div>
            <div class="description">首批GUI商用电脑，价格昂贵</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">TCP/IP 协议</div>
            <div class="description">ARPANET标准协议，互联网基石</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">IBM PC XT</div>
            <div class="description">首次标配硬盘的PC</div>
        </div>
    </div>
</div>

<!-- 1982 -->
<div class="year-bar">
    <div class="year-label">1982</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Commodore 64</div>
            <div class="description">史上最畅销的单一计算机型号</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">SMTP 协议</div>
            <div class="description">简单邮件传输协议</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Intel 80286</div>
            <div class="description">16位处理器，保护模式</div>
        </div>
    </div>
</div>

<!-- 1981 -->
<div class="year-bar">
    <div class="year-label">1981</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">IBM PC 5150</div>
            <div class="description">个人电脑标准确立，开放架构</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">RS-232 串口</div>
            <div class="description">串行通信标准，外设连接</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">MS-DOS 1.0</div>
            <div class="description">微软磁盘操作系统</div>
        </div>
    </div>
</div>

<!-- 1978 -->
<div class="year-bar">
    <div class="year-label">1978</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">5.25英寸软盘</div>
            <div class="description">个人电脑软件和数据交换介质</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">调制解调器</div>
            <div class="description">300波特率，电话线数据传输</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Intel 8086</div>
            <div class="description">16位微处理器，x86架构起点</div>
        </div>
    </div>
</div>

<!-- 1977 -->
<div class="year-bar">
    <div class="year-label">1977</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Apple II</div>
            <div class="description">首款大规模成功的个人电脑</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">IEEE 488 总线</div>
            <div class="description">通用接口总线，仪器连接</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">DEC VAX-11/780</div>
            <div class="description">32位小型机，虚拟地址扩展</div>
        </div>
    </div>
</div>

<!-- 1976 -->
<div class="year-bar">
    <div class="year-label">1976</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Apple I</div>
            <div class="description">苹果公司首款产品，计算机爱好者</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">X.25 协议</div>
            <div class="description">分组交换网络协议</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Cray-1</div>
            <div class="description">首台商业超级计算机</div>
        </div>
    </div>
</div>

<!-- 1973 -->
<div class="year-bar">
    <div class="year-label">1973</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">Xerox Alto</div>
            <div class="description">首台GUI工作站，鼠标和窗口</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">以太网发明</div>
            <div class="description">Xerox PARC发明，局域网标准</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Intel 8080</div>
            <div class="description">8位微处理器，个人电脑基础</div>
        </div>
    </div>
</div>

<!-- 1971 -->
<div class="year-bar">
    <div class="year-label">1971</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">8英寸软盘</div>
            <div class="description">IBM发明，首个便携式存储介质</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">ARPANET 首次连接</div>
            <div class="description">UCLA和斯坦福，互联网诞生</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Intel 4004</div>
            <div class="description">首款商用微处理器，4位</div>
        </div>
    </div>
</div>

<!-- 1969 -->
<div class="year-bar">
    <div class="year-label">1969</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">哑终端</div>
            <div class="description">连接大型机的字符终端</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">ARPANET IMP</div>
            <div class="description">接口消息处理器，网络节点</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">Unix 操作系统</div>
            <div class="description">AT&T贝尔实验室开发</div>
        </div>
    </div>
</div>

<!-- 1964 -->
<div class="year-bar">
    <div class="year-label">1964</div>
    <div class="step">
        <div class="era1 terminal active" data-category="terminal">
            <div class="layer-indicator">终端</div>
            <div class="hardware-name">BASIC 语言</div>
            <div class="description">达特茅斯学院，易学编程语言</div>
        </div>
        <div class="era1 transmission" data-category="transmission">
            <div class="layer-indicator">传输</div>
            <div class="hardware-name">9-track 磁带</div>
            <div class="description">数据存储和交换标准格式</div>
        </div>
        <div class="era1 service" data-category="service">
            <div class="layer-indicator">服务</div>
            <div class="hardware-name">IBM System/360</div>
            <div class="description">兼容性计算机系列，统一架构</div>
        </div>
    </div>
</div>
</div>

<script>
function filterByCategory(category, button) {
    const allItems = document.querySelectorAll('.step > div');
    const allButtons = document.querySelectorAll('.tab-button');
    
    // 移除所有按钮的active类
    allButtons.forEach(btn => btn.classList.remove('active'));
    button.classList.add('active');
    
    if (category === 'all') {
        allItems.forEach(item => {
            item.classList.remove('active');
            item.classList.add('show-all');
        });
    } else {
        allItems.forEach(item => {
            item.classList.remove('show-all');
            if (item.dataset.category === category) {
                item.classList.add('active');
            } else {
                item.classList.remove('active');
            }
        });
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('计算机发展时间轴天梯图加载完成');
});
</script>

</body>
</html>

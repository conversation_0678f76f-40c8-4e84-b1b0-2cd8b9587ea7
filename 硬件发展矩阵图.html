<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="UTF-8">
<title>硬件发展矩阵图 - 三阶段三层次演进</title>
<style>
    body {
        font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
        margin: 0;
        background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
        color: #eee;
        min-height: 100vh;
    }
    
    .container {
        max-width: 1400px;
        margin: 0 auto;
        padding: 20px;
    }
    
    h1 {
        text-align: center;
        font-size: 2.5em;
        margin: 30px 0;
        background: linear-gradient(45deg, #4facfe, #00f2fe);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        text-shadow: 0 0 30px rgba(79, 172, 254, 0.3);
    }
    
    .subtitle {
        text-align: center;
        font-size: 1.2em;
        color: #aaa;
        margin-bottom: 40px;
    }
    
    .matrix-container {
        display: grid;
        grid-template-columns: 200px 1fr 1fr 1fr;
        grid-template-rows: auto repeat(3, 1fr);
        gap: 2px;
        background: #333;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    }
    
    .header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 20px;
        text-align: center;
        font-weight: bold;
        font-size: 1.1em;
    }
    
    .era-header {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        padding: 20px;
        text-align: center;
        font-weight: bold;
    }
    
    .era-title {
        font-size: 1.3em;
        margin-bottom: 8px;
    }
    
    .era-period {
        font-size: 0.9em;
        opacity: 0.9;
    }
    
    .era-core {
        font-size: 0.8em;
        margin-top: 5px;
        font-style: italic;
    }
    
    .layer-header {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        padding: 20px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        font-weight: bold;
    }
    
    .layer-title {
        font-size: 1.2em;
        margin-bottom: 5px;
    }
    
    .layer-desc {
        font-size: 0.8em;
        opacity: 0.9;
        font-weight: normal;
    }
    
    .cell {
        background: #1a1a2e;
        padding: 20px;
        min-height: 200px;
        display: flex;
        flex-direction: column;
        transition: all 0.3s ease;
        border: 1px solid transparent;
    }
    
    .cell:hover {
        background: #252545;
        border-color: #4facfe;
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(79, 172, 254, 0.2);
    }
    
    .cell-title {
        font-size: 1.1em;
        font-weight: bold;
        color: #4facfe;
        margin-bottom: 15px;
        border-bottom: 2px solid #4facfe;
        padding-bottom: 8px;
    }
    
    .hardware-list {
        flex: 1;
    }
    
    .hardware-category {
        margin-bottom: 15px;
    }
    
    .category-title {
        font-size: 0.9em;
        color: #f5576c;
        font-weight: bold;
        margin-bottom: 8px;
    }
    
    .hardware-items {
        font-size: 0.85em;
        line-height: 1.6;
        color: #ccc;
    }
    
    .characteristics {
        margin-top: 15px;
        padding-top: 15px;
        border-top: 1px solid #333;
    }
    
    .char-title {
        font-size: 0.8em;
        color: #00f2fe;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .char-content {
        font-size: 0.8em;
        color: #aaa;
        line-height: 1.5;
    }
    
    .timeline {
        margin-top: 50px;
        text-align: center;
    }
    
    .timeline h2 {
        color: #4facfe;
        margin-bottom: 30px;
    }
    
    .timeline-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        max-width: 800px;
        margin: 0 auto;
        position: relative;
    }
    
    .timeline-line {
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 2px;
        background: linear-gradient(90deg, #4facfe, #f5576c, #00f2fe);
        z-index: 1;
    }
    
    .timeline-point {
        background: #1a1a2e;
        border: 3px solid #4facfe;
        border-radius: 50%;
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 0.9em;
        z-index: 2;
        position: relative;
    }
    
    .stage1 { border-color: #4facfe; }
    .stage2 { border-color: #f5576c; }
    .stage3 { border-color: #00f2fe; }
</style>
</head>
<body>
<div class="container">
    <h1>硬件发展矩阵图</h1>
    <div class="subtitle">三阶段·三层次·硬件演进全景</div>
    
    <div class="matrix-container">
        <!-- 表头 -->
        <div class="header">硬件层次</div>
        <div class="era-header">
            <div class="era-title">第一阶段：计算机时代</div>
            <div class="era-period">(约1970s - 1995)</div>
            <div class="era-core">核心：计算的诞生与普及</div>
        </div>
        <div class="era-header">
            <div class="era-title">第二阶段：互联网时代</div>
            <div class="era-period">(约1995 - 2010)</div>
            <div class="era-core">核心：连接的价值</div>
        </div>
        <div class="era-header">
            <div class="era-title">第三阶段：AI与云时代</div>
            <div class="era-period">(约2010 - 至今)</div>
            <div class="era-core">核心：数据的智能</div>
        </div>
        
        <!-- 终端层 -->
        <div class="layer-header">
            <div class="layer-title">① 终端 (Terminal)</div>
            <div class="layer-desc">人机交互的入口</div>
        </div>
        <div class="cell">
            <div class="cell-title">个人电脑 (PC)</div>
            <div class="hardware-list">
                <div class="hardware-category">
                    <div class="category-title">核心硬件：</div>
                    <div class="hardware-items">CPU (Intel 8086, 486, Pentium)、内存(RAM)、硬盘(HDD)、主板</div>
                </div>
                <div class="hardware-category">
                    <div class="category-title">交互硬件：</div>
                    <div class="hardware-items">CRT显示器、键盘、鼠标</div>
                </div>
            </div>
            <div class="characteristics">
                <div class="char-title">特征：</div>
                <div class="char-content">终端与计算服务高度统一。PC既是操作界面，也是计算处理中心。它是一个信息孤岛。</div>
            </div>
        </div>
        <div class="cell">
            <div class="cell-title">多样化接入设备</div>
            <div class="hardware-list">
                <div class="hardware-category">
                    <div class="category-title">核心硬件：</div>
                    <div class="hardware-items">PC性能增强、显卡(GPU)图形加速、笔记本电脑普及</div>
                </div>
                <div class="hardware-category">
                    <div class="category-title">新兴硬件：</div>
                    <div class="hardware-items">功能手机(Nokia)、PDA、早期智能手机(BlackBerry)</div>
                </div>
            </div>
            <div class="characteristics">
                <div class="char-title">特征：</div>
                <div class="char-content">从"计算器"转变为"浏览器"或"信息窗口"。更关注网络连接能力和便携性。</div>
            </div>
        </div>
        <div class="cell">
            <div class="cell-title">泛在智能终端</div>
            <div class="hardware-list">
                <div class="hardware-category">
                    <div class="category-title">核心硬件：</div>
                    <div class="hardware-items">智能手机SoC芯片(集成CPU/GPU/NPU/ISP)</div>
                </div>
                <div class="hardware-category">
                    <div class="category-title">新兴硬件：</div>
                    <div class="hardware-items">可穿戴设备、IoT设备、智能音箱、智能汽车、AR/VR头显</div>
                </div>
            </div>
            <div class="characteristics">
                <div class="char-title">特征：</div>
                <div class="char-content">从"信息窗口"转变为"数据采集器"和"智能交互入口"。内置AI能力，多模态交互。</div>
            </div>
        </div>
        
        <!-- 传输层 -->
        <div class="layer-header">
            <div class="layer-title">② 传输 (Transmission)</div>
            <div class="layer-desc">数据流动的管道</div>
        </div>
        <div class="cell">
            <div class="cell-title">本地化连接</div>
            <div class="hardware-list">
                <div class="hardware-category">
                    <div class="category-title">核心硬件：</div>
                    <div class="hardware-items">主板总线(Bus)、串口(RS-232)、并口</div>
                </div>
                <div class="hardware-category">
                    <div class="category-title">网络硬件：</div>
                    <div class="hardware-items">以太网卡(Ethernet Card)、集线器(Hub)、局域网(LAN)</div>
                </div>
            </div>
            <div class="characteristics">
                <div class="char-title">特征：</div>
                <div class="char-content">传输距离短、速率慢、协议不统一。主要用于共享外设或小范围文件交换。</div>
            </div>
        </div>
        <div class="cell">
            <div class="cell-title">全球互联</div>
            <div class="hardware-list">
                <div class="hardware-category">
                    <div class="category-title">接入硬件：</div>
                    <div class="hardware-items">调制解调器(Modem)、ADSL/Cable Modem宽带</div>
                </div>
                <div class="hardware-category">
                    <div class="category-title">网络骨干：</div>
                    <div class="hardware-items">路由器(Router)、交换机(Switch)、海底光缆、Wi-Fi、2G/3G</div>
                </div>
            </div>
            <div class="characteristics">
                <div class="char-title">特征：</div>
                <div class="char-content">带宽、延迟和覆盖范围成为关键指标。连接本身成为核心能力。</div>
            </div>
        </div>
        <div class="cell">
            <div class="cell-title">高速泛在网络</div>
            <div class="hardware-list">
                <div class="hardware-category">
                    <div class="category-title">核心硬件：</div>
                    <div class="hardware-items">5G基站和终端芯片、Wi-Fi 6/6E、千兆光纤</div>
                </div>
                <div class="hardware-category">
                    <div class="category-title">数据中心内部：</div>
                    <div class="hardware-items">InfiniBand、NVLink超高带宽互联技术</div>
                </div>
            </div>
            <div class="characteristics">
                <div class="char-title">特征：</div>
                <div class="char-content">保证海量数据(视频和传感器数据)的实时、可靠传输。支撑AI集群计算。</div>
            </div>
        </div>
        
        <!-- 服务层 -->
        <div class="layer-header">
            <div class="layer-title">③ 服务 (Service)</div>
            <div class="layer-desc">计算与存储的核心</div>
        </div>
        <div class="cell">
            <div class="cell-title">大型机/小型机</div>
            <div class="hardware-list">
                <div class="hardware-category">
                    <div class="category-title">核心硬件：</div>
                    <div class="hardware-items">专有高性能中央处理器、海量存储</div>
                </div>
                <div class="hardware-category">
                    <div class="category-title">形态：</div>
                    <div class="hardware-items">巨大机柜、专门机房、专业运维</div>
                </div>
            </div>
            <div class="characteristics">
                <div class="char-title">特征：</div>
                <div class="char-content">集中式计算服务，用户通过哑终端连接。PC的出现将集中式服务分布到桌面。</div>
            </div>
        </div>
        <div class="cell">
            <div class="cell-title">分布式服务器/数据中心</div>
            <div class="hardware-list">
                <div class="hardware-category">
                    <div class="category-title">核心硬件：</div>
                    <div class="hardware-items">机架式服务器、刀片服务器</div>
                </div>
                <div class="hardware-category">
                    <div class="category-title">存储硬件：</div>
                    <div class="hardware-items">网络附加存储(NAS)、存储区域网络(SAN)</div>
                </div>
            </div>
            <div class="characteristics">
                <div class="char-title">特征：</div>
                <div class="char-content">客户端-服务器架构成为主流。服务器集群追求高可用性和可扩展性。</div>
            </div>
        </div>
        <div class="cell">
            <div class="cell-title">超大规模云数据中心/边缘计算</div>
            <div class="hardware-list">
                <div class="hardware-category">
                    <div class="category-title">AI加速器：</div>
                    <div class="hardware-items">GPU(NVIDIA)、ASIC(Google TPU)、FPGA</div>
                </div>
                <div class="hardware-category">
                    <div class="category-title">形态：</div>
                    <div class="hardware-items">超大规模数据中心(AWS/Azure/GCP)、边缘计算节点</div>
                </div>
            </div>
            <div class="characteristics">
                <div class="char-title">特征：</div>
                <div class="char-content">从提供"资源"升级为提供"能力"，特别是AI训练和推理能力。异构计算和领域专用架构。</div>
            </div>
        </div>
    </div>
    
    <div class="timeline">
        <h2>硬件发展驱动力变迁</h2>
        <div class="timeline-container">
            <div class="timeline-line"></div>
            <div class="timeline-point stage1">计算驱动</div>
            <div class="timeline-point stage2">连接驱动</div>
            <div class="timeline-point stage3">智能驱动</div>
        </div>
    </div>
</div>
</body>
</html>